<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isAdmin()) {
    header('Location: index.php');
    exit();
}

$page_title = 'Manajemen Pesan';
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$message_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Handle actions
if ($action === 'mark_read' && $message_id > 0) {
    try {
        $stmt = $pdo->prepare("UPDATE contact_messages SET status = 'read', updated_at = NOW() WHERE id = ?");
        $stmt->execute([$message_id]);
        header('Location: messages.php?success=' . urlencode('Pesan ditandai sebagai sudah dibaca.'));
        exit();
    } catch (Exception $e) {
        $error_message = 'Gagal memperbarui status pesan: ' . $e->getMessage();
    }
}

if ($action === 'mark_unread' && $message_id > 0) {
    try {
        $stmt = $pdo->prepare("UPDATE contact_messages SET status = 'unread', updated_at = NOW() WHERE id = ?");
        $stmt->execute([$message_id]);
        header('Location: messages.php?success=' . urlencode('Pesan ditandai sebagai belum dibaca.'));
        exit();
    } catch (Exception $e) {
        $error_message = 'Gagal memperbarui status pesan: ' . $e->getMessage();
    }
}

if ($action === 'delete' && $message_id > 0) {
    try {
        $stmt = $pdo->prepare("DELETE FROM contact_messages WHERE id = ?");
        $stmt->execute([$message_id]);
        header('Location: messages.php?success=' . urlencode('Pesan berhasil dihapus.'));
        exit();
    } catch (Exception $e) {
        $error_message = 'Gagal menghapus pesan: ' . $e->getMessage();
    }
}

// Get message data for view
$message = null;
if ($action === 'view' && $message_id > 0) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM contact_messages WHERE id = ?");
        $stmt->execute([$message_id]);
        $message = $stmt->fetch();
        
        if (!$message) {
            header('Location: messages.php?error=' . urlencode('Pesan tidak ditemukan.'));
            exit();
        }
        
        // Mark as read when viewed
        if ($message['status'] === 'unread') {
            $stmt = $pdo->prepare("UPDATE contact_messages SET status = 'read', updated_at = NOW() WHERE id = ?");
            $stmt->execute([$message_id]);
            $message['status'] = 'read';
        }
    } catch (Exception $e) {
        $error_message = 'Gagal memuat data pesan: ' . $e->getMessage();
    }
}

// Get all messages for list view
if ($action === 'list') {
    try {
        $search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
        $status_filter = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
        
        $sql = "SELECT * FROM contact_messages WHERE 1=1";
        $params = [];
        
        if ($search) {
            $sql .= " AND (name LIKE ? OR email LIKE ? OR message LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if ($status_filter) {
            $sql .= " AND status = ?";
            $params[] = $status_filter;
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $messages = $stmt->fetchAll();
    } catch (Exception $e) {
        $error_message = 'Gagal memuat data pesan: ' . $e->getMessage();
        $messages = [];
    }
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-envelope me-2"></i>
                    <?php echo $action === 'view' ? 'Detail Pesan' : 'Manajemen Pesan'; ?>
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <?php if ($action === 'view'): ?>
                        <a href="messages.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Kembali
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Alert Messages -->
            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($_GET['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($_GET['error']) || isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars(isset($_GET['error']) ? $_GET['error'] : $error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($action === 'list'): ?>
                <!-- Messages List -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="m-0 font-weight-bold text-primary">Daftar Pesan Kontak</h6>
                            </div>
                            <div class="col-md-6">
                                <form method="GET" class="d-flex">
                                    <input type="text" name="search" class="form-control form-control-sm me-2" 
                                           placeholder="Cari pesan..." value="<?php echo htmlspecialchars($search ?? ''); ?>">
                                    <select name="status" class="form-select form-select-sm me-2">
                                        <option value="">Semua Status</option>
                                        <option value="unread" <?php echo ($status_filter ?? '') === 'unread' ? 'selected' : ''; ?>>Belum Dibaca</option>
                                        <option value="read" <?php echo ($status_filter ?? '') === 'read' ? 'selected' : ''; ?>>Sudah Dibaca</option>
                                    </select>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($messages)): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="200">Pengirim</th>
                                            <th>Pesan</th>
                                            <th width="100">Status</th>
                                            <th width="120">Tanggal</th>
                                            <th width="150">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($messages as $msg): ?>
                                            <tr class="<?php echo $msg['status'] === 'unread' ? 'table-warning' : ''; ?>">
                                                <td>
                                                    <strong><?php echo htmlspecialchars($msg['name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($msg['email']); ?></small>
                                                    <?php if ($msg['phone']): ?>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($msg['phone']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="message-preview">
                                                        <?php echo substr(htmlspecialchars($msg['message']), 0, 150) . '...'; ?>
                                                    </div>
                                                    <?php if ($msg['subject']): ?>
                                                        <small class="text-muted">
                                                            <strong>Subjek:</strong> <?php echo htmlspecialchars($msg['subject']); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $msg['status'] === 'unread' ? 'warning' : 'success'; ?>">
                                                        <?php echo $msg['status'] === 'unread' ? 'Belum Dibaca' : 'Sudah Dibaca'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <small><?php echo formatDate($msg['created_at']); ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="messages.php?action=view&id=<?php echo $msg['id']; ?>" 
                                                           class="btn btn-outline-info" title="Lihat">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($msg['status'] === 'unread'): ?>
                                                            <a href="messages.php?action=mark_read&id=<?php echo $msg['id']; ?>" 
                                                               class="btn btn-outline-success" title="Tandai Dibaca">
                                                                <i class="fas fa-check"></i>
                                                            </a>
                                                        <?php else: ?>
                                                            <a href="messages.php?action=mark_unread&id=<?php echo $msg['id']; ?>" 
                                                               class="btn btn-outline-warning" title="Tandai Belum Dibaca">
                                                                <i class="fas fa-undo"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="messages.php?action=delete&id=<?php echo $msg['id']; ?>" 
                                                           class="btn btn-outline-danger" title="Hapus"
                                                           data-action="delete" data-item="pesan dari <?php echo htmlspecialchars($msg['name']); ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-envelope display-1 text-muted mb-3"></i>
                                <h5 class="text-muted">Belum ada pesan</h5>
                                <p class="text-muted">Pesan dari formulir kontak akan muncul di sini.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            
            <?php elseif ($action === 'view' && $message): ?>
                <!-- Message View -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">Detail Pesan</h6>
                        <div>
                            <span class="badge bg-<?php echo $message['status'] === 'unread' ? 'warning' : 'success'; ?>">
                                <?php echo $message['status'] === 'unread' ? 'Belum Dibaca' : 'Sudah Dibaca'; ?>
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="message-header mb-4">
                                    <h4><?php echo htmlspecialchars($message['name']); ?></h4>
                                    <p class="text-muted mb-1">
                                        <i class="fas fa-envelope me-2"></i>
                                        <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>">
                                            <?php echo htmlspecialchars($message['email']); ?>
                                        </a>
                                    </p>
                                    <?php if ($message['phone']): ?>
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-phone me-2"></i>
                                            <a href="tel:<?php echo htmlspecialchars($message['phone']); ?>">
                                                <?php echo htmlspecialchars($message['phone']); ?>
                                            </a>
                                        </p>
                                    <?php endif; ?>
                                    <?php if ($message['subject']): ?>
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-tag me-2"></i>
                                            <strong>Subjek:</strong> <?php echo htmlspecialchars($message['subject']); ?>
                                        </p>
                                    <?php endif; ?>
                                    <p class="text-muted">
                                        <i class="fas fa-calendar me-2"></i>
                                        <?php echo formatDate($message['created_at']); ?>
                                    </p>
                                </div>
                                
                                <div class="message-content">
                                    <h6>Pesan:</h6>
                                    <div class="border p-3 rounded bg-light">
                                        <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Aksi Cepat</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>?subject=Re: <?php echo urlencode($message['subject'] ?: 'Pesan Anda'); ?>" 
                                               class="btn btn-primary">
                                                <i class="fas fa-reply me-2"></i>
                                                Balas via Email
                                            </a>
                                            
                                            <?php if ($message['phone']): ?>
                                                <a href="https://wa.me/<?php echo str_replace(['+', '-', ' ', '(', ')'], '', $message['phone']); ?>?text=Halo <?php echo urlencode($message['name']); ?>, terima kasih atas pesan Anda." 
                                                   class="btn btn-success" target="_blank">
                                                    <i class="fab fa-whatsapp me-2"></i>
                                                    WhatsApp
                                                </a>
                                                
                                                <a href="tel:<?php echo htmlspecialchars($message['phone']); ?>" 
                                                   class="btn btn-info">
                                                    <i class="fas fa-phone me-2"></i>
                                                    Telepon
                                                </a>
                                            <?php endif; ?>
                                            
                                            <?php if ($message['status'] === 'unread'): ?>
                                                <a href="messages.php?action=mark_read&id=<?php echo $message['id']; ?>" 
                                                   class="btn btn-outline-success">
                                                    <i class="fas fa-check me-2"></i>
                                                    Tandai Dibaca
                                                </a>
                                            <?php else: ?>
                                                <a href="messages.php?action=mark_unread&id=<?php echo $message['id']; ?>" 
                                                   class="btn btn-outline-warning">
                                                    <i class="fas fa-undo me-2"></i>
                                                    Tandai Belum Dibaca
                                                </a>
                                            <?php endif; ?>
                                            
                                            <a href="messages.php?action=delete&id=<?php echo $message['id']; ?>" 
                                               class="btn btn-outline-danger"
                                               data-action="delete" data-item="pesan ini">
                                                <i class="fas fa-trash me-2"></i>
                                                Hapus Pesan
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
