<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isAdmin()) {
    header('Location: index.php');
    exit();
}

$page_title = 'Manajemen Produk';
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitizeInput($_POST['name']);
    $description = sanitizeInput($_POST['description']);
    $price = (float)$_POST['price'];
    $category = sanitizeInput($_POST['category']);
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    $status = sanitizeInput($_POST['status']);
    
    // Handle image upload
    $image_name = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../assets/images/products/';
        $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
        
        if (in_array($file_extension, $allowed_extensions)) {
            $image_name = uniqid() . '.' . $file_extension;
            $upload_path = $upload_dir . $image_name;
            
            if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                $error_message = 'Gagal mengupload gambar.';
            }
        } else {
            $error_message = 'Format gambar tidak didukung. Gunakan JPG, PNG, atau GIF.';
        }
    }
    
    if (!isset($error_message)) {
        try {
            if ($action === 'add') {
                $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category, image, is_featured, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
                $stmt->execute([$name, $description, $price, $category, $image_name, $is_featured, $status]);
                $success_message = 'Produk berhasil ditambahkan.';
            } elseif ($action === 'edit' && $product_id > 0) {
                if ($image_name) {
                    // Delete old image
                    $stmt = $pdo->prepare("SELECT image FROM products WHERE id = ?");
                    $stmt->execute([$product_id]);
                    $old_product = $stmt->fetch();
                    if ($old_product && $old_product['image'] && file_exists('../assets/images/products/' . $old_product['image'])) {
                        unlink('../assets/images/products/' . $old_product['image']);
                    }
                    
                    $stmt = $pdo->prepare("UPDATE products SET name = ?, description = ?, price = ?, category = ?, image = ?, is_featured = ?, status = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$name, $description, $price, $category, $image_name, $is_featured, $status, $product_id]);
                } else {
                    $stmt = $pdo->prepare("UPDATE products SET name = ?, description = ?, price = ?, category = ?, is_featured = ?, status = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$name, $description, $price, $category, $is_featured, $status, $product_id]);
                }
                $success_message = 'Produk berhasil diperbarui.';
            }
            
            if (isset($success_message)) {
                header('Location: products.php?success=' . urlencode($success_message));
                exit();
            }
        } catch (Exception $e) {
            $error_message = 'Terjadi kesalahan: ' . $e->getMessage();
        }
    }
}

// Handle delete action
if ($action === 'delete' && $product_id > 0) {
    try {
        // Get product info for image deletion
        $stmt = $pdo->prepare("SELECT image FROM products WHERE id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch();
        
        // Delete product
        $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
        $stmt->execute([$product_id]);
        
        // Delete image file
        if ($product && $product['image'] && file_exists('../assets/images/products/' . $product['image'])) {
            unlink('../assets/images/products/' . $product['image']);
        }
        
        header('Location: products.php?success=' . urlencode('Produk berhasil dihapus.'));
        exit();
    } catch (Exception $e) {
        $error_message = 'Gagal menghapus produk: ' . $e->getMessage();
    }
}

// Get product data for edit
$product = null;
if (($action === 'edit' || $action === 'view') && $product_id > 0) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM products WHERE id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch();
        
        if (!$product) {
            header('Location: products.php?error=' . urlencode('Produk tidak ditemukan.'));
            exit();
        }
    } catch (Exception $e) {
        $error_message = 'Gagal memuat data produk: ' . $e->getMessage();
    }
}

// Get all products for list view
if ($action === 'list') {
    try {
        $search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
        $category_filter = isset($_GET['category']) ? sanitizeInput($_GET['category']) : '';
        $status_filter = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
        
        $sql = "SELECT * FROM products WHERE 1=1";
        $params = [];
        
        if ($search) {
            $sql .= " AND (name LIKE ? OR description LIKE ?)";
            $params[] = "%$search%";
            $params[] = "%$search%";
        }
        
        if ($category_filter) {
            $sql .= " AND category = ?";
            $params[] = $category_filter;
        }
        
        if ($status_filter) {
            $sql .= " AND status = ?";
            $params[] = $status_filter;
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $products = $stmt->fetchAll();
    } catch (Exception $e) {
        $error_message = 'Gagal memuat data produk: ' . $e->getMessage();
        $products = [];
    }
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-box me-2"></i>
                    <?php 
                    switch($action) {
                        case 'add': echo 'Tambah Produk'; break;
                        case 'edit': echo 'Edit Produk'; break;
                        case 'view': echo 'Detail Produk'; break;
                        default: echo 'Manajemen Produk'; break;
                    }
                    ?>
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <?php if ($action === 'list'): ?>
                        <a href="products.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Tambah Produk
                        </a>
                    <?php else: ?>
                        <a href="products.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Kembali
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Alert Messages -->
            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($_GET['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($_GET['error']) || isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars(isset($_GET['error']) ? $_GET['error'] : $error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($action === 'list'): ?>
                <!-- Products List -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="m-0 font-weight-bold text-primary">Daftar Produk</h6>
                            </div>
                            <div class="col-md-6">
                                <form method="GET" class="d-flex">
                                    <input type="text" name="search" class="form-control form-control-sm me-2" 
                                           placeholder="Cari produk..." value="<?php echo htmlspecialchars($search ?? ''); ?>">
                                    <select name="category" class="form-select form-select-sm me-2">
                                        <option value="">Semua Kategori</option>
                                        <option value="air_isi_ulang" <?php echo ($category_filter ?? '') === 'air_isi_ulang' ? 'selected' : ''; ?>>Air Isi Ulang</option>
                                        <option value="air_kemasan" <?php echo ($category_filter ?? '') === 'air_kemasan' ? 'selected' : ''; ?>>Air Kemasan</option>
                                    </select>
                                    <select name="status" class="form-select form-select-sm me-2">
                                        <option value="">Semua Status</option>
                                        <option value="active" <?php echo ($status_filter ?? '') === 'active' ? 'selected' : ''; ?>>Aktif</option>
                                        <option value="inactive" <?php echo ($status_filter ?? '') === 'inactive' ? 'selected' : ''; ?>>Tidak Aktif</option>
                                    </select>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($products)): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="80">Gambar</th>
                                            <th>Nama Produk</th>
                                            <th width="120">Kategori</th>
                                            <th width="120">Harga</th>
                                            <th width="80">Status</th>
                                            <th width="100">Unggulan</th>
                                            <th width="120">Tanggal</th>
                                            <th width="150">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($products as $prod): ?>
                                            <tr>
                                                <td>
                                                    <?php if ($prod['image']): ?>
                                                        <img src="../assets/images/products/<?php echo $prod['image']; ?>" 
                                                             alt="<?php echo htmlspecialchars($prod['name']); ?>"
                                                             class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-light d-flex align-items-center justify-content-center" 
                                                             style="width: 60px; height: 60px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($prod['name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo substr(htmlspecialchars($prod['description']), 0, 80) . '...'; ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $prod['category'] === 'air_isi_ulang' ? 'primary' : 'success'; ?>">
                                                        <?php echo $prod['category'] === 'air_isi_ulang' ? 'Air Isi Ulang' : 'Air Kemasan'; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatCurrency($prod['price']); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $prod['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                        <?php echo $prod['status'] === 'active' ? 'Aktif' : 'Tidak Aktif'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($prod['is_featured']): ?>
                                                        <span class="badge bg-warning">
                                                            <i class="fas fa-star"></i> Unggulan
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small><?php echo formatDate($prod['created_at']); ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="products.php?action=view&id=<?php echo $prod['id']; ?>" 
                                                           class="btn btn-outline-info" title="Lihat">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="products.php?action=edit&id=<?php echo $prod['id']; ?>" 
                                                           class="btn btn-outline-primary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="products.php?action=delete&id=<?php echo $prod['id']; ?>" 
                                                           class="btn btn-outline-danger" title="Hapus"
                                                           data-action="delete" data-item="<?php echo htmlspecialchars($prod['name']); ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-box display-1 text-muted mb-3"></i>
                                <h5 class="text-muted">Belum ada produk</h5>
                                <p class="text-muted">Klik tombol "Tambah Produk" untuk menambahkan produk pertama.</p>
                                <a href="products.php?action=add" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Tambah Produk
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php elseif ($action === 'add' || $action === 'edit'): ?>
                <!-- Product Form -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <?php echo $action === 'add' ? 'Tambah Produk Baru' : 'Edit Produk'; ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" data-autosave="product_form">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Nama Produk *</label>
                                        <input type="text" class="form-control" id="name" name="name"
                                               value="<?php echo $product ? htmlspecialchars($product['name']) : ''; ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="description" class="form-label">Deskripsi *</label>
                                        <textarea class="form-control" id="description" name="description" rows="4"
                                                  data-autoresize required><?php echo $product ? htmlspecialchars($product['description']) : ''; ?></textarea>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="price" class="form-label">Harga *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">Rp</span>
                                                    <input type="number" class="form-control" id="price" name="price"
                                                           value="<?php echo $product ? $product['price'] : ''; ?>"
                                                           min="0" step="100" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="category" class="form-label">Kategori *</label>
                                                <select class="form-select" id="category" name="category" required>
                                                    <option value="">Pilih Kategori</option>
                                                    <option value="air_isi_ulang" <?php echo ($product && $product['category'] === 'air_isi_ulang') ? 'selected' : ''; ?>>
                                                        Air Isi Ulang
                                                    </option>
                                                    <option value="air_kemasan" <?php echo ($product && $product['category'] === 'air_kemasan') ? 'selected' : ''; ?>>
                                                        Air Kemasan
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="image" class="form-label">Gambar Produk</label>
                                        <input type="file" class="form-control" id="image" name="image"
                                               accept="image/jpeg,image/jpg,image/png,image/gif">
                                        <div class="form-text">Format: JPG, PNG, GIF. Maksimal 2MB.</div>

                                        <?php if ($product && $product['image']): ?>
                                            <div class="mt-3">
                                                <img src="../assets/images/products/<?php echo $product['image']; ?>"
                                                     alt="Current image" class="img-thumbnail"
                                                     style="max-width: 200px; max-height: 200px;">
                                                <div class="form-text">Gambar saat ini</div>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status *</label>
                                        <select class="form-select" id="status" name="status" required>
                                            <option value="active" <?php echo (!$product || $product['status'] === 'active') ? 'selected' : ''; ?>>
                                                Aktif
                                            </option>
                                            <option value="inactive" <?php echo ($product && $product['status'] === 'inactive') ? 'selected' : ''; ?>>
                                                Tidak Aktif
                                            </option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured"
                                                   <?php echo ($product && $product['is_featured']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="is_featured">
                                                <strong>Produk Unggulan</strong>
                                                <div class="form-text">Tampilkan di halaman utama</div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="border-top pt-3 mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    <?php echo $action === 'add' ? 'Tambah Produk' : 'Simpan Perubahan'; ?>
                                </button>
                                <a href="products.php" class="btn btn-secondary ms-2">
                                    <i class="fas fa-times me-1"></i>
                                    Batal
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

            <?php elseif ($action === 'view' && $product): ?>
                <!-- Product View -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Detail Produk</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <?php if ($product['image']): ?>
                                    <img src="../assets/images/products/<?php echo $product['image']; ?>"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                         class="img-fluid rounded shadow">
                                <?php else: ?>
                                    <div class="bg-light d-flex align-items-center justify-content-center rounded"
                                         style="height: 300px;">
                                        <i class="fas fa-image display-1 text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-8">
                                <h3><?php echo htmlspecialchars($product['name']); ?></h3>

                                <div class="mb-3">
                                    <span class="badge bg-<?php echo $product['category'] === 'air_isi_ulang' ? 'primary' : 'success'; ?> me-2">
                                        <?php echo $product['category'] === 'air_isi_ulang' ? 'Air Isi Ulang' : 'Air Kemasan'; ?>
                                    </span>
                                    <span class="badge bg-<?php echo $product['status'] === 'active' ? 'success' : 'secondary'; ?> me-2">
                                        <?php echo $product['status'] === 'active' ? 'Aktif' : 'Tidak Aktif'; ?>
                                    </span>
                                    <?php if ($product['is_featured']): ?>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-star"></i> Unggulan
                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="mb-3">
                                    <h4 class="text-primary"><?php echo formatCurrency($product['price']); ?></h4>
                                </div>

                                <div class="mb-3">
                                    <h6>Deskripsi:</h6>
                                    <p><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">
                                        <strong>Dibuat:</strong> <?php echo formatDate($product['created_at']); ?><br>
                                        <?php if ($product['updated_at']): ?>
                                            <strong>Diperbarui:</strong> <?php echo formatDate($product['updated_at']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>

                                <div class="mt-4">
                                    <a href="products.php?action=edit&id=<?php echo $product['id']; ?>"
                                       class="btn btn-primary">
                                        <i class="fas fa-edit me-1"></i>
                                        Edit Produk
                                    </a>
                                    <a href="products.php" class="btn btn-secondary ms-2">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        Kembali
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
