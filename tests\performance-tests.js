/**
 * Performance Testing Suite for Agen Air Fazza
 * Tests Core Web Vitals and other performance metrics
 */

class PerformanceTester {
    constructor() {
        this.metrics = {
            lcp: null,
            fid: null,
            cls: null,
            fcp: null,
            ttfb: null,
            loadTime: null,
            domElements: 0,
            imageCount: 0,
            scriptCount: 0,
            stylesheetCount: 0
        };
        this.thresholds = {
            lcp: 2500,      // Good: < 2.5s
            fid: 100,       // Good: < 100ms
            cls: 0.1,       // Good: < 0.1
            fcp: 1800,      // Good: < 1.8s
            ttfb: 600,      // Good: < 600ms
            loadTime: 3000, // Good: < 3s
            domElements: 1500,
            imageCount: 50,
            scriptCount: 10,
            stylesheetCount: 10
        };
        this.init();
    }

    init() {
        this.measureCoreWebVitals();
        this.measureBasicMetrics();
        this.measureResourceCounts();
        this.setupPerformanceObserver();
    }

    // Measure Core Web Vitals
    measureCoreWebVitals() {
        // Largest Contentful Paint (LCP)
        if ('PerformanceObserver' in window) {
            try {
                const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    this.metrics.lcp = lastEntry.startTime;
                });
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            } catch (e) {
                console.warn('LCP measurement not supported');
            }

            // First Input Delay (FID)
            try {
                const fidObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        this.metrics.fid = entry.processingStart - entry.startTime;
                    });
                });
                fidObserver.observe({ entryTypes: ['first-input'] });
            } catch (e) {
                console.warn('FID measurement not supported');
            }

            // Cumulative Layout Shift (CLS)
            try {
                let clsValue = 0;
                const clsObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (!entry.hadRecentInput) {
                            clsValue += entry.value;
                        }
                    });
                    this.metrics.cls = clsValue;
                });
                clsObserver.observe({ entryTypes: ['layout-shift'] });
            } catch (e) {
                console.warn('CLS measurement not supported');
            }
        }

        // First Contentful Paint (FCP)
        if (performance.getEntriesByType) {
            const paintEntries = performance.getEntriesByType('paint');
            const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
            if (fcpEntry) {
                this.metrics.fcp = fcpEntry.startTime;
            }
        }
    }

    // Measure basic performance metrics
    measureBasicMetrics() {
        if (performance.timing) {
            const timing = performance.timing;
            
            // Time to First Byte (TTFB)
            this.metrics.ttfb = timing.responseStart - timing.navigationStart;
            
            // Total Load Time
            this.metrics.loadTime = timing.loadEventEnd - timing.navigationStart;
        }

        // Modern Navigation Timing API
        if (performance.getEntriesByType) {
            const navigationEntries = performance.getEntriesByType('navigation');
            if (navigationEntries.length > 0) {
                const nav = navigationEntries[0];
                this.metrics.ttfb = nav.responseStart;
                this.metrics.loadTime = nav.loadEventEnd;
            }
        }
    }

    // Count DOM elements and resources
    measureResourceCounts() {
        // DOM elements
        this.metrics.domElements = document.querySelectorAll('*').length;
        
        // Images
        this.metrics.imageCount = document.querySelectorAll('img').length;
        
        // Scripts
        this.metrics.scriptCount = document.querySelectorAll('script').length;
        
        // Stylesheets
        this.metrics.stylesheetCount = document.querySelectorAll('link[rel="stylesheet"]').length;
    }

    // Setup performance observer for additional metrics
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.entryType === 'measure') {
                            console.log(`Custom measure: ${entry.name} = ${entry.duration}ms`);
                        }
                    });
                });
                observer.observe({ entryTypes: ['measure'] });
            } catch (e) {
                console.warn('Performance Observer not fully supported');
            }
        }
    }

    // Get all metrics
    getMetrics() {
        return { ...this.metrics };
    }

    // Evaluate performance against thresholds
    evaluatePerformance() {
        const results = {
            overall: 'good',
            scores: {},
            issues: [],
            recommendations: []
        };

        // Evaluate each metric
        Object.keys(this.thresholds).forEach(metric => {
            const value = this.metrics[metric];
            const threshold = this.thresholds[metric];
            
            if (value !== null && value !== undefined) {
                let score = 'good';
                let status = 'pass';
                
                if (metric === 'cls') {
                    // CLS: lower is better
                    if (value > 0.25) {
                        score = 'poor';
                        status = 'fail';
                    } else if (value > 0.1) {
                        score = 'needs-improvement';
                        status = 'warning';
                    }
                } else {
                    // Other metrics: lower is better
                    if (value > threshold * 2) {
                        score = 'poor';
                        status = 'fail';
                    } else if (value > threshold) {
                        score = 'needs-improvement';
                        status = 'warning';
                    }
                }
                
                results.scores[metric] = {
                    value,
                    threshold,
                    score,
                    status
                };
                
                if (status === 'fail') {
                    results.overall = 'poor';
                    results.issues.push(`${metric.toUpperCase()}: ${value}${this.getUnit(metric)} (threshold: ${threshold}${this.getUnit(metric)})`);
                } else if (status === 'warning' && results.overall === 'good') {
                    results.overall = 'needs-improvement';
                }
            }
        });

        // Generate recommendations
        results.recommendations = this.generateRecommendations(results.scores);

        return results;
    }

    // Get unit for metric
    getUnit(metric) {
        switch (metric) {
            case 'lcp':
            case 'fid':
            case 'fcp':
            case 'ttfb':
            case 'loadTime':
                return 'ms';
            case 'cls':
                return '';
            default:
                return '';
        }
    }

    // Generate performance recommendations
    generateRecommendations(scores) {
        const recommendations = [];

        if (scores.lcp && scores.lcp.status !== 'pass') {
            recommendations.push({
                metric: 'LCP',
                issue: 'Largest Contentful Paint is slow',
                solutions: [
                    'Optimize images and use modern formats (WebP, AVIF)',
                    'Implement lazy loading for images',
                    'Minimize render-blocking resources',
                    'Use a Content Delivery Network (CDN)',
                    'Optimize server response times'
                ]
            });
        }

        if (scores.fid && scores.fid.status !== 'pass') {
            recommendations.push({
                metric: 'FID',
                issue: 'First Input Delay is high',
                solutions: [
                    'Minimize JavaScript execution time',
                    'Remove unused JavaScript',
                    'Split code and load only what\'s needed',
                    'Use web workers for heavy computations',
                    'Optimize third-party scripts'
                ]
            });
        }

        if (scores.cls && scores.cls.status !== 'pass') {
            recommendations.push({
                metric: 'CLS',
                issue: 'Cumulative Layout Shift is high',
                solutions: [
                    'Set explicit dimensions for images and videos',
                    'Reserve space for ads and embeds',
                    'Avoid inserting content above existing content',
                    'Use CSS aspect-ratio for responsive images',
                    'Preload fonts to prevent font swapping'
                ]
            });
        }

        if (scores.fcp && scores.fcp.status !== 'pass') {
            recommendations.push({
                metric: 'FCP',
                issue: 'First Contentful Paint is slow',
                solutions: [
                    'Eliminate render-blocking resources',
                    'Minify CSS and JavaScript',
                    'Remove unused CSS',
                    'Optimize fonts loading',
                    'Use critical CSS inlining'
                ]
            });
        }

        if (scores.ttfb && scores.ttfb.status !== 'pass') {
            recommendations.push({
                metric: 'TTFB',
                issue: 'Time to First Byte is high',
                solutions: [
                    'Optimize server configuration',
                    'Use a faster hosting provider',
                    'Implement server-side caching',
                    'Optimize database queries',
                    'Use a CDN for static assets'
                ]
            });
        }

        if (scores.domElements && scores.domElements.status !== 'pass') {
            recommendations.push({
                metric: 'DOM',
                issue: 'Too many DOM elements',
                solutions: [
                    'Simplify page structure',
                    'Remove unnecessary elements',
                    'Use CSS instead of extra HTML elements',
                    'Implement virtual scrolling for long lists',
                    'Split content across multiple pages'
                ]
            });
        }

        return recommendations;
    }

    // Generate performance report
    generateReport() {
        const evaluation = this.evaluatePerformance();
        
        return {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            metrics: this.metrics,
            evaluation: evaluation,
            coreWebVitals: {
                lcp: this.metrics.lcp,
                fid: this.metrics.fid,
                cls: this.metrics.cls
            },
            recommendations: evaluation.recommendations
        };
    }

    // Start continuous monitoring
    startMonitoring(interval = 5000) {
        setInterval(() => {
            this.measureResourceCounts();
            console.log('Performance metrics updated:', this.getMetrics());
        }, interval);
    }

    // Measure custom performance mark
    mark(name) {
        if (performance.mark) {
            performance.mark(name);
        }
    }

    // Measure time between two marks
    measure(name, startMark, endMark) {
        if (performance.measure) {
            performance.measure(name, startMark, endMark);
        }
    }

    // Clear performance marks and measures
    clearMarks() {
        if (performance.clearMarks) {
            performance.clearMarks();
        }
        if (performance.clearMeasures) {
            performance.clearMeasures();
        }
    }
}

// Performance testing utilities
const PerformanceUtils = {
    // Test image loading performance
    async testImageLoading() {
        const images = document.querySelectorAll('img');
        const results = [];
        
        for (const img of images) {
            const startTime = performance.now();
            
            if (img.complete) {
                results.push({
                    src: img.src,
                    loadTime: 0,
                    cached: true
                });
            } else {
                await new Promise((resolve) => {
                    img.onload = () => {
                        const loadTime = performance.now() - startTime;
                        results.push({
                            src: img.src,
                            loadTime,
                            cached: false
                        });
                        resolve();
                    };
                    img.onerror = () => {
                        results.push({
                            src: img.src,
                            loadTime: -1,
                            error: true
                        });
                        resolve();
                    };
                });
            }
        }
        
        return results;
    },

    // Test script loading performance
    getScriptMetrics() {
        const scripts = performance.getEntriesByType('resource')
            .filter(entry => entry.initiatorType === 'script');
        
        return scripts.map(script => ({
            name: script.name,
            duration: script.duration,
            size: script.transferSize,
            startTime: script.startTime
        }));
    },

    // Test CSS loading performance
    getStylesheetMetrics() {
        const stylesheets = performance.getEntriesByType('resource')
            .filter(entry => entry.initiatorType === 'link' || entry.name.includes('.css'));
        
        return stylesheets.map(stylesheet => ({
            name: stylesheet.name,
            duration: stylesheet.duration,
            size: stylesheet.transferSize,
            startTime: stylesheet.startTime
        }));
    },

    // Memory usage (if available)
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };
        }
        return null;
    }
};

// Initialize performance testing when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Wait a bit for the page to settle
    setTimeout(() => {
        window.performanceTester = new PerformanceTester();
        
        // Log initial metrics after 3 seconds
        setTimeout(() => {
            const report = window.performanceTester.generateReport();
            console.log('Performance Report:', report);
            
            // Store report for testing dashboard
            if (window.testingDashboard) {
                window.testingDashboard.setPerformanceReport(report);
            }
        }, 3000);
    }, 1000);
});

// Export for use in other scripts
if (typeof window !== 'undefined') {
    window.PerformanceTester = PerformanceTester;
    window.PerformanceUtils = PerformanceUtils;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PerformanceTester, PerformanceUtils };
}
