<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Get current page from URL parameter
$page = isset($_GET['page']) ? $_GET['page'] : 'home';

// Define allowed pages
$allowed_pages = ['home', 'products', 'profile', 'gallery', 'contact'];

// Validate page parameter
if (!in_array($page, $allowed_pages)) {
    $page = 'home';
}

// Set page title based on current page
$page_titles = [
    'home' => 'Beranda - Agen Air Fazza',
    'products' => 'Produk - Agen Air Fazza',
    'profile' => 'Profil Usaha - Agen Air Fazza',
    'gallery' => 'Galeri & Testimoni - Agen Air Fazza',
    'contact' => 'Kontak Kami - Agen Air Fazza'
];

$page_title = $page_titles[$page];
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0">
    <title><?php echo $page_title; ?></title>

    <!-- Mobile-specific meta tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Air Fazza">
    <meta name="application-name" content="Air Fazza">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="theme-color" content="#2563eb">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Favicons and App Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/icons/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/icons/apple-touch-icon.png">

    <!-- SEO Meta Tags -->
    <meta name="description" content="Agen Air Fazza - Penyedia air isi ulang dan air mineral kemasan berkualitas tinggi dengan layanan terpercaya di wilayah Anda.">
    <meta name="keywords" content="air isi ulang, air mineral, agen air, air bersih, air sehat, Fazza">
    <meta name="author" content="Agen Air Fazza">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="Agen Air Fazza - Penyedia air isi ulang dan air mineral kemasan berkualitas tinggi">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo getCurrentUrl(); ?>">
    <meta property="og:locale" content="id_ID">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="Agen Air Fazza - Penyedia air isi ulang dan air mineral kemasan berkualitas tinggi">
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/global.css" rel="stylesheet">
    <link href="assets/css/mobile.css" rel="stylesheet">
    <link href="assets/css/responsive-utils.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Header -->
    <?php include 'includes/header.php'; ?>
    
    <!-- Main Content -->
    <main id="main-content">
        <?php
        // Include the appropriate page content
        $page_file = "pages/{$page}.php";
        if (file_exists($page_file)) {
            include $page_file;
        } else {
            include 'pages/404.php';
        }
        ?>
    </main>
    
    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
    
    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/mobile.js"></script>

    <!-- Testing Scripts (only in development) -->
    <?php if (isset($_GET['test']) || (defined('ENVIRONMENT') && ENVIRONMENT === 'development')): ?>
    <script src="tests/performance-tests.js"></script>
    <script src="tests/automated-tests.js"></script>
    <?php endif; ?>
    
    <!-- Initialize AOS -->
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });
    </script>
</body>
</html>
