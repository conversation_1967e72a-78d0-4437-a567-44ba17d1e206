/**
 * Mobile-specific JavaScript enhancements
 * Enhanced mobile interactions and optimizations for Agen Air Fazza
 */

// Mobile detection and device information
const MobileDetector = {
    isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
    isTablet: /iPad|Android(?=.*\bMobile\b)(?=.*\bSafari\b)|Android(?=.*\bMobile\b)/i.test(navigator.userAgent),
    isIOS: /iPad|iPhone|iPod/.test(navigator.userAgent),
    isAndroid: /Android/.test(navigator.userAgent),
    
    getScreenSize() {
        return {
            width: window.innerWidth,
            height: window.innerHeight,
            orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
        };
    },
    
    isSmallScreen() {
        return window.innerWidth < 768;
    }
};

// Mobile-optimized navigation
class MobileNavigation {
    constructor() {
        this.navbar = document.querySelector('.navbar');
        this.navbarToggler = document.querySelector('.navbar-toggler');
        this.navbarCollapse = document.querySelector('.navbar-collapse');
        this.navLinks = document.querySelectorAll('.nav-link');
        
        this.init();
    }
    
    init() {
        if (!this.navbar) return;
        
        this.setupMobileMenu();
        this.setupTouchInteractions();
        this.setupScrollBehavior();
        this.setupOrientationChange();
    }
    
    setupMobileMenu() {
        // Close mobile menu when clicking nav links
        this.navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (MobileDetector.isSmallScreen()) {
                    const bsCollapse = new bootstrap.Collapse(this.navbarCollapse, {
                        hide: true
                    });
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (MobileDetector.isSmallScreen() && 
                !this.navbar.contains(e.target) && 
                this.navbarCollapse.classList.contains('show')) {
                
                const bsCollapse = new bootstrap.Collapse(this.navbarCollapse, {
                    hide: true
                });
            }
        });
    }
    
    setupTouchInteractions() {
        // Add touch feedback to nav links
        this.navLinks.forEach(link => {
            link.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });
            
            link.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });
    }
    
    setupScrollBehavior() {
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', () => {
            if (!MobileDetector.isSmallScreen()) return;
            
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // Scrolling down - hide navbar
                this.navbar.style.transform = 'translateY(-100%)';
            } else {
                // Scrolling up - show navbar
                this.navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        });
    }
    
    setupOrientationChange() {
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                // Close mobile menu on orientation change
                if (this.navbarCollapse.classList.contains('show')) {
                    const bsCollapse = new bootstrap.Collapse(this.navbarCollapse, {
                        hide: true
                    });
                }
            }, 100);
        });
    }
}

// Touch gesture handler
class TouchGestureHandler {
    constructor() {
        this.startX = 0;
        this.startY = 0;
        this.endX = 0;
        this.endY = 0;
        this.minSwipeDistance = 50;
        
        this.init();
    }
    
    init() {
        if (!MobileDetector.isMobile) return;
        
        document.addEventListener('touchstart', this.handleTouchStart.bind(this));
        document.addEventListener('touchend', this.handleTouchEnd.bind(this));
    }
    
    handleTouchStart(e) {
        this.startX = e.touches[0].clientX;
        this.startY = e.touches[0].clientY;
    }
    
    handleTouchEnd(e) {
        this.endX = e.changedTouches[0].clientX;
        this.endY = e.changedTouches[0].clientY;
        
        this.handleSwipe();
    }
    
    handleSwipe() {
        const deltaX = this.endX - this.startX;
        const deltaY = this.endY - this.startY;
        
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // Horizontal swipe
            if (Math.abs(deltaX) > this.minSwipeDistance) {
                if (deltaX > 0) {
                    this.onSwipeRight();
                } else {
                    this.onSwipeLeft();
                }
            }
        } else {
            // Vertical swipe
            if (Math.abs(deltaY) > this.minSwipeDistance) {
                if (deltaY > 0) {
                    this.onSwipeDown();
                } else {
                    this.onSwipeUp();
                }
            }
        }
    }
    
    onSwipeLeft() {
        // Handle left swipe (e.g., next image in gallery)
        const event = new CustomEvent('swipeLeft');
        document.dispatchEvent(event);
    }
    
    onSwipeRight() {
        // Handle right swipe (e.g., previous image in gallery)
        const event = new CustomEvent('swipeRight');
        document.dispatchEvent(event);
    }
    
    onSwipeUp() {
        // Handle up swipe
        const event = new CustomEvent('swipeUp');
        document.dispatchEvent(event);
    }
    
    onSwipeDown() {
        // Handle down swipe
        const event = new CustomEvent('swipeDown');
        document.dispatchEvent(event);
    }
}

// Mobile performance optimizer
class MobilePerformanceOptimizer {
    constructor() {
        this.init();
    }
    
    init() {
        this.optimizeImages();
        this.optimizeAnimations();
        this.setupIntersectionObserver();
        this.optimizeScrolling();
    }
    
    optimizeImages() {
        // Lazy load images on mobile
        const images = document.querySelectorAll('img[loading="lazy"]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        }
    }
    
    optimizeAnimations() {
        // Reduce animations on mobile for better performance
        if (MobileDetector.isMobile) {
            const style = document.createElement('style');
            style.textContent = `
                @media (max-width: 767.98px) {
                    * {
                        animation-duration: 0.3s !important;
                        transition-duration: 0.3s !important;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    setupIntersectionObserver() {
        // Optimize AOS animations for mobile
        if (typeof AOS !== 'undefined' && MobileDetector.isMobile) {
            AOS.init({
                duration: 300,
                once: true,
                disable: 'mobile'
            });
        }
    }
    
    optimizeScrolling() {
        // Passive scroll listeners for better performance
        let ticking = false;
        
        function updateScrollPosition() {
            // Update scroll-dependent elements
            ticking = false;
        }
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        }, { passive: true });
    }
}

// Mobile form enhancements
class MobileFormEnhancer {
    constructor() {
        this.init();
    }
    
    init() {
        this.preventZoomOnInputFocus();
        this.enhanceFormValidation();
        this.setupVirtualKeyboardHandling();
    }
    
    preventZoomOnInputFocus() {
        // Prevent zoom on input focus for iOS
        if (MobileDetector.isIOS) {
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (input.style.fontSize === '' || parseFloat(input.style.fontSize) < 16) {
                    input.style.fontSize = '16px';
                }
            });
        }
    }
    
    enhanceFormValidation() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
            });
        });
    }
    
    validateField(field) {
        const isValid = field.checkValidity();
        
        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }
    }
    
    setupVirtualKeyboardHandling() {
        // Handle virtual keyboard on mobile
        if (MobileDetector.isMobile) {
            const inputs = document.querySelectorAll('input, textarea');
            
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    setTimeout(() => {
                        input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }, 300);
                });
            });
        }
    }
}

// Mobile-specific utilities
const MobileUtils = {
    // Vibrate device (if supported)
    vibrate(pattern = [100]) {
        if ('vibrate' in navigator) {
            navigator.vibrate(pattern);
        }
    },
    
    // Check if device is in standalone mode (PWA)
    isStandalone() {
        return window.matchMedia('(display-mode: standalone)').matches ||
               window.navigator.standalone === true;
    },
    
    // Get device pixel ratio
    getPixelRatio() {
        return window.devicePixelRatio || 1;
    },
    
    // Check if device supports touch
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },
    
    // Smooth scroll to element
    scrollToElement(element, offset = 0) {
        const elementPosition = element.offsetTop - offset;
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
    },
    
    // Show mobile-friendly notification
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `mobile-notification mobile-notification-${type}`;
        notification.textContent = message;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            z-index: 9999;
            font-size: 0.9rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 90%;
            text-align: center;
        `;
        
        document.body.appendChild(notification);
        
        // Add vibration feedback
        if (type === 'error') {
            this.vibrate([100, 50, 100]);
        } else if (type === 'success') {
            this.vibrate([100]);
        }
        
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(-50%) translateY(-20px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }
};

// Initialize mobile enhancements when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize on mobile devices
    if (MobileDetector.isMobile || MobileDetector.isSmallScreen()) {
        new MobileNavigation();
        new TouchGestureHandler();
        new MobilePerformanceOptimizer();
        new MobileFormEnhancer();
        
        // Add mobile class to body
        document.body.classList.add('mobile-device');
        
        // Add device-specific classes
        if (MobileDetector.isIOS) {
            document.body.classList.add('ios-device');
        }
        if (MobileDetector.isAndroid) {
            document.body.classList.add('android-device');
        }
    }
    
    // Add screen size class
    if (MobileDetector.isSmallScreen()) {
        document.body.classList.add('small-screen');
    }
});

// Handle window resize
window.addEventListener('resize', () => {
    if (MobileDetector.isSmallScreen()) {
        document.body.classList.add('small-screen');
    } else {
        document.body.classList.remove('small-screen');
    }
});

// Export for use in other scripts
window.MobileDetector = MobileDetector;
window.MobileUtils = MobileUtils;
