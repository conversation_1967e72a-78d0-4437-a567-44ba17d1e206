/* Responsive Utilities & Advanced Mobile Optimizations */
/* Advanced responsive utilities for enhanced mobile experience */

/* ===== RESPONSIVE BREAKPOINT VARIABLES ===== */
:root {
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;
}

/* ===== CONTAINER QUERIES (Future-ready) ===== */
@supports (container-type: inline-size) {
    .container-query {
        container-type: inline-size;
    }
    
    @container (max-width: 400px) {
        .cq-small {
            font-size: 0.875rem;
        }
    }
}

/* ===== ADVANCED RESPONSIVE GRID ===== */

/* Responsive grid system */
.responsive-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

@media (min-width: 576px) {
    .responsive-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .responsive-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 992px) {
    .responsive-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Auto-fit grid */
.auto-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

@media (max-width: 575.98px) {
    .auto-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */

/* Fluid typography */
.fluid-text {
    font-size: clamp(1rem, 2.5vw, 1.5rem);
}

.fluid-heading {
    font-size: clamp(1.5rem, 5vw, 3rem);
    line-height: 1.2;
}

.fluid-subheading {
    font-size: clamp(1.25rem, 3vw, 2rem);
    line-height: 1.3;
}

/* Responsive text alignment */
.text-responsive {
    text-align: center;
}

@media (min-width: 768px) {
    .text-responsive {
        text-align: left;
    }
}

/* ===== RESPONSIVE SPACING ===== */

/* Responsive margins */
.m-responsive {
    margin: 1rem;
}

@media (min-width: 768px) {
    .m-responsive {
        margin: 2rem;
    }
}

@media (min-width: 992px) {
    .m-responsive {
        margin: 3rem;
    }
}

/* Responsive padding */
.p-responsive {
    padding: 1rem;
}

@media (min-width: 768px) {
    .p-responsive {
        padding: 2rem;
    }
}

@media (min-width: 992px) {
    .p-responsive {
        padding: 3rem;
    }
}

/* Section spacing */
.section-responsive {
    padding: 2rem 0;
}

@media (min-width: 768px) {
    .section-responsive {
        padding: 4rem 0;
    }
}

@media (min-width: 992px) {
    .section-responsive {
        padding: 6rem 0;
    }
}

/* ===== RESPONSIVE COMPONENTS ===== */

/* Responsive cards */
.card-responsive {
    margin-bottom: 1rem;
}

.card-responsive .card-body {
    padding: 1rem;
}

@media (min-width: 768px) {
    .card-responsive {
        margin-bottom: 1.5rem;
    }
    
    .card-responsive .card-body {
        padding: 1.5rem;
    }
}

@media (min-width: 992px) {
    .card-responsive {
        margin-bottom: 2rem;
    }
    
    .card-responsive .card-body {
        padding: 2rem;
    }
}

/* Responsive buttons */
.btn-responsive {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

@media (min-width: 768px) {
    .btn-responsive {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

@media (min-width: 992px) {
    .btn-responsive {
        padding: 1rem 2rem;
        font-size: 1.125rem;
    }
}

/* ===== MOBILE-FIRST UTILITIES ===== */

/* Mobile-first display utilities */
.d-mobile-block {
    display: block;
}

.d-mobile-none {
    display: none;
}

@media (min-width: 768px) {
    .d-mobile-block {
        display: none;
    }
    
    .d-mobile-none {
        display: block;
    }
}

/* Mobile-first flex utilities */
.flex-mobile-column {
    flex-direction: column;
}

@media (min-width: 768px) {
    .flex-mobile-column {
        flex-direction: row;
    }
}

/* Mobile-first width utilities */
.w-mobile-100 {
    width: 100%;
}

@media (min-width: 768px) {
    .w-mobile-100 {
        width: auto;
    }
}

/* ===== TOUCH-FRIENDLY ENHANCEMENTS ===== */

/* Touch targets */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Touch feedback */
.touch-feedback {
    transition: all 0.15s ease;
}

.touch-feedback:active {
    transform: scale(0.95);
    opacity: 0.8;
}

/* Swipe indicators */
.swipe-indicator {
    position: relative;
}

.swipe-indicator::after {
    content: '← Swipe →';
    position: absolute;
    bottom: -2rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: var(--text-light);
    opacity: 0.7;
}

@media (min-width: 768px) {
    .swipe-indicator::after {
        display: none;
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Reduce motion for better performance */
@media (prefers-reduced-motion: reduce) {
    .respect-motion * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* GPU acceleration for smooth animations */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* Optimize images for different screen densities */
.responsive-image {
    width: 100%;
    height: auto;
    object-fit: cover;
}

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .responsive-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* High contrast mode support */
@media (prefers-contrast: high) {
    .high-contrast {
        --primary-color: #000080;
        --text-dark: #000000;
        --text-light: #333333;
        --border-color: #000000;
        --bg-light: #ffffff;
    }
}

/* Focus management for mobile */
@media (max-width: 767.98px) {
    .mobile-focus:focus {
        outline: 3px solid var(--primary-color);
        outline-offset: 2px;
        border-radius: 0.25rem;
    }
}

/* Screen reader utilities */
.sr-only-mobile {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

@media (min-width: 768px) {
    .sr-only-mobile {
        position: static;
        width: auto;
        height: auto;
        padding: inherit;
        margin: inherit;
        overflow: visible;
        clip: auto;
        white-space: normal;
        border: inherit;
    }
}

/* ===== ADVANCED LAYOUT UTILITIES ===== */

/* Responsive aspect ratios */
.aspect-ratio-responsive {
    aspect-ratio: 16/9;
}

@media (max-width: 767.98px) {
    .aspect-ratio-responsive {
        aspect-ratio: 4/3;
    }
}

/* Responsive object positioning */
.object-responsive {
    object-fit: cover;
    object-position: center;
}

@media (max-width: 767.98px) {
    .object-responsive {
        object-position: center top;
    }
}

/* ===== RESPONSIVE ANIMATIONS ===== */

/* Mobile-optimized animations */
@media (max-width: 767.98px) {
    .animate-mobile {
        animation-duration: 0.3s;
        animation-timing-function: ease-out;
    }
}

@media (min-width: 768px) {
    .animate-mobile {
        animation-duration: 0.6s;
        animation-timing-function: ease-in-out;
    }
}

/* Responsive transform utilities */
.transform-responsive {
    transform: scale(0.9);
}

@media (min-width: 768px) {
    .transform-responsive {
        transform: scale(1);
    }
}

/* ===== RESPONSIVE DEBUGGING ===== */

/* Debug breakpoints (remove in production) */
.debug-breakpoints::before {
    content: 'XS';
    position: fixed;
    top: 0;
    right: 0;
    background: red;
    color: white;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    z-index: 9999;
}

@media (min-width: 576px) {
    .debug-breakpoints::before {
        content: 'SM';
        background: orange;
    }
}

@media (min-width: 768px) {
    .debug-breakpoints::before {
        content: 'MD';
        background: yellow;
        color: black;
    }
}

@media (min-width: 992px) {
    .debug-breakpoints::before {
        content: 'LG';
        background: green;
    }
}

@media (min-width: 1200px) {
    .debug-breakpoints::before {
        content: 'XL';
        background: blue;
    }
}

@media (min-width: 1400px) {
    .debug-breakpoints::before {
        content: 'XXL';
        background: purple;
    }
}
