<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Test Suite - Agen Air Fazza</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 2rem auto;
            max-width: 1200px;
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #2563eb, #06b6d4);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        
        .test-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .test-content {
            padding: 2rem;
        }
        
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .test-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .test-button:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .progress-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .test-log {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 1rem;
            border-radius: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-size: 0.875rem;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            opacity: 0.9;
            font-size: 0.875rem;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        .status-pending { background: #6c757d; }
        .status-running { background: #ffc107; animation: pulse 1s infinite; }
        .status-passed { background: #28a745; }
        .status-failed { background: #dc3545; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .test-result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .test-result-item:last-child {
            border-bottom: none;
        }
        
        .report-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 2rem;
        }
        
        .btn-report {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .btn-report:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="test-container">
            <!-- Header -->
            <div class="test-header">
                <h1>
                    <i class="fas fa-vial me-3"></i>
                    Complete Test Suite
                </h1>
                <p class="lead mb-0">Comprehensive Quality Assurance for Agen Air Fazza</p>
                <p class="mt-2 opacity-75">Automated testing, performance analysis, and quality reports</p>
            </div>
            
            <!-- Content -->
            <div class="test-content">
                <!-- Quick Start -->
                <div class="test-section">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3>
                                <i class="fas fa-rocket me-2 text-primary"></i>
                                Quick Start Testing
                            </h3>
                            <p class="text-muted mb-0">Run all automated tests and generate comprehensive reports</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <button id="runAllTests" class="test-button">
                                <i class="fas fa-play me-2"></i>
                                Run All Tests
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Progress Section -->
                <div class="test-section" id="progressSection" style="display: none;">
                    <h4>
                        <i class="fas fa-chart-line me-2 text-info"></i>
                        Test Progress
                    </h4>
                    <div class="progress-container">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span id="progressText">Initializing tests...</span>
                            <span id="progressPercentage">0%</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div id="progressBar" class="progress-bar bg-info" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="test-log" id="testLog">
                        <div>🚀 Test suite initialized</div>
                        <div>📋 Ready to run comprehensive tests...</div>
                    </div>
                </div>
                
                <!-- Results Section -->
                <div class="test-section" id="resultsSection" style="display: none;">
                    <h4>
                        <i class="fas fa-chart-bar me-2 text-success"></i>
                        Test Results
                    </h4>
                    
                    <!-- Metrics -->
                    <div class="metric-grid" id="metricsGrid">
                        <div class="metric-card">
                            <div class="metric-value" id="totalTests">0</div>
                            <div class="metric-label">Total Tests</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="passedTests">0</div>
                            <div class="metric-label">Passed</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="failedTests">0</div>
                            <div class="metric-label">Failed</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="successRate">0%</div>
                            <div class="metric-label">Success Rate</div>
                        </div>
                    </div>
                    
                    <!-- Detailed Results -->
                    <div class="mt-4">
                        <h5>Detailed Results</h5>
                        <div id="detailedResults" class="border rounded p-3">
                            <!-- Results will be populated here -->
                        </div>
                    </div>
                    
                    <!-- Report Actions -->
                    <div class="report-actions">
                        <button class="btn-report" onclick="downloadReport('html')">
                            <i class="fas fa-file-code me-2"></i>
                            Download HTML Report
                        </button>
                        <button class="btn-report" onclick="downloadReport('json')">
                            <i class="fas fa-file-alt me-2"></i>
                            Download JSON Report
                        </button>
                        <button class="btn-report" onclick="downloadReport('csv')">
                            <i class="fas fa-table me-2"></i>
                            Download CSV Report
                        </button>
                        <button class="btn-report" onclick="printReport()">
                            <i class="fas fa-print me-2"></i>
                            Print Report
                        </button>
                        <button class="btn-report" onclick="emailReport()">
                            <i class="fas fa-envelope me-2"></i>
                            Email Report
                        </button>
                    </div>
                </div>
                
                <!-- Manual Testing -->
                <div class="test-section">
                    <h4>
                        <i class="fas fa-clipboard-check me-2 text-warning"></i>
                        Manual Testing Resources
                    </h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <a href="manual-testing-checklist.md" target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-list-check me-2"></i>
                                    Manual Testing Checklist
                                </a>
                                <a href="testing-dashboard.html" target="_blank" class="btn btn-outline-info">
                                    <i class="fas fa-tachometer-alt me-2"></i>
                                    Testing Dashboard
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <a href="../responsive-test.html" target="_blank" class="btn btn-outline-success">
                                    <i class="fas fa-mobile-alt me-2"></i>
                                    Responsive Test Page
                                </a>
                                <button class="btn btn-outline-warning" onclick="openTestPages()">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    Open All Pages for Testing
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="automated-tests.js"></script>
    <script src="performance-tests.js"></script>
    <script src="test-report-generator.js"></script>
    
    <script>
        class CompleteTestRunner {
            constructor() {
                this.testSuite = null;
                this.performanceTester = null;
                this.reportGenerator = null;
                this.isRunning = false;
                this.results = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.initializeTestSuite();
            }

            setupEventListeners() {
                document.getElementById('runAllTests').addEventListener('click', () => {
                    this.runCompleteTestSuite();
                });
            }

            initializeTestSuite() {
                try {
                    this.testSuite = initializeTestSuite();
                    this.performanceTester = new PerformanceTester();
                    this.reportGenerator = new TestReportGenerator();
                    this.log('✅ Test suite initialized successfully');
                } catch (error) {
                    this.log(`❌ Failed to initialize test suite: ${error.message}`);
                }
            }

            async runCompleteTestSuite() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.showProgress();
                this.updateRunButton(true);
                
                try {
                    this.log('🚀 Starting comprehensive test suite...');
                    this.updateProgress(10, 'Running automated tests...');
                    
                    // Run automated tests
                    this.results = await this.testSuite.runAllTests();
                    this.updateProgress(60, 'Analyzing performance metrics...');
                    
                    // Wait for performance metrics to settle
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                    // Get performance report
                    const performanceReport = this.performanceTester.generateReport();
                    this.updateProgress(80, 'Generating comprehensive report...');
                    
                    // Generate comprehensive report
                    this.reportGenerator.setTestResults(this.results);
                    this.reportGenerator.setPerformanceMetrics(performanceReport);
                    
                    this.updateProgress(100, 'Tests completed successfully!');
                    this.displayResults();
                    this.log('✅ All tests completed successfully');
                    
                } catch (error) {
                    this.log(`❌ Test execution failed: ${error.message}`);
                } finally {
                    this.isRunning = false;
                    this.updateRunButton(false);
                }
            }

            showProgress() {
                document.getElementById('progressSection').style.display = 'block';
                document.getElementById('progressSection').scrollIntoView({ behavior: 'smooth' });
            }

            updateProgress(percentage, text) {
                document.getElementById('progressBar').style.width = `${percentage}%`;
                document.getElementById('progressPercentage').textContent = `${percentage}%`;
                document.getElementById('progressText').textContent = text;
            }

            displayResults() {
                // Show results section
                document.getElementById('resultsSection').style.display = 'block';
                document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
                
                // Update metrics
                document.getElementById('totalTests').textContent = this.results.total;
                document.getElementById('passedTests').textContent = this.results.passed;
                document.getElementById('failedTests').textContent = this.results.failed;
                
                const successRate = ((this.results.passed / this.results.total) * 100).toFixed(1);
                document.getElementById('successRate').textContent = `${successRate}%`;
                
                // Display detailed results
                const detailedResults = document.getElementById('detailedResults');
                detailedResults.innerHTML = '';
                
                // Group by category
                const categories = {};
                this.results.details.forEach(test => {
                    if (!categories[test.category]) {
                        categories[test.category] = [];
                    }
                    categories[test.category].push(test);
                });
                
                // Display each category
                Object.entries(categories).forEach(([category, tests]) => {
                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'mb-3';
                    
                    const passed = tests.filter(t => t.status === 'passed').length;
                    const total = tests.length;
                    
                    categoryDiv.innerHTML = `
                        <h6 class="border-bottom pb-2">${category} (${passed}/${total})</h6>
                        ${tests.map(test => `
                            <div class="test-result-item">
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator status-${test.status}"></span>
                                    <span>${test.name}</span>
                                </div>
                                <small class="text-muted">${test.duration}ms</small>
                            </div>
                        `).join('')}
                    `;
                    
                    detailedResults.appendChild(categoryDiv);
                });
            }

            updateRunButton(isRunning) {
                const button = document.getElementById('runAllTests');
                if (isRunning) {
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Running Tests...';
                    button.disabled = true;
                } else {
                    button.innerHTML = '<i class="fas fa-play me-2"></i>Run All Tests';
                    button.disabled = false;
                }
            }

            log(message) {
                const logContainer = document.getElementById('testLog');
                const logEntry = document.createElement('div');
                
                const timestamp = new Date().toLocaleTimeString();
                logEntry.textContent = `[${timestamp}] ${message}`;
                
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }
        }

        // Global functions for report actions
        function downloadReport(format) {
            if (window.testRunner && window.testRunner.reportGenerator) {
                window.testRunner.reportGenerator.downloadReport(format);
            } else {
                alert('Please run tests first to generate a report.');
            }
        }

        function printReport() {
            if (window.testRunner && window.testRunner.reportGenerator) {
                window.testRunner.reportGenerator.printReport();
            } else {
                alert('Please run tests first to generate a report.');
            }
        }

        function emailReport() {
            if (window.testRunner && window.testRunner.reportGenerator) {
                window.testRunner.reportGenerator.emailReport();
            } else {
                alert('Please run tests first to generate a report.');
            }
        }

        function openTestPages() {
            const pages = [
                '../index.php?page=home',
                '../index.php?page=products',
                '../index.php?page=profile',
                '../index.php?page=gallery',
                '../index.php?page=contact'
            ];
            
            pages.forEach(page => {
                window.open(page, '_blank');
            });
        }

        // Initialize test runner
        window.testRunner = new CompleteTestRunner();
    </script>
</body>
</html>
