<?php
// Get filter parameters
$category_filter = isset($_GET['category']) ? $_GET['category'] : '';
$search_query = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';

// Get products based on filters
if ($search_query) {
    // Search products by name or description
    $sql = "SELECT * FROM products WHERE status = 'active' AND (name LIKE ? OR description LIKE ?)";
    $params = ["%$search_query%", "%$search_query%"];
    
    if ($category_filter) {
        $sql .= " AND category = ?";
        $params[] = $category_filter;
    }
    
    $sql .= " ORDER BY is_featured DESC, created_at DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll();
} else {
    $products = getProducts($category_filter);
}

$company_info = getCompanyInfo();
?>

<!-- Page Header -->
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row align-items-center py-5">
            <div class="col-lg-8" data-aos="fade-right">
                <h1 class="display-4 fw-bold mb-3">Produk Kami</h1>
                <p class="lead mb-0">
                    Temukan berbagai pilihan air bersih berkualitas tinggi untuk kebutuhan keluarga Anda. 
                    Semua produk telah melewati standar kualitas terbaik.
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <div class="page-header-icon">
                    <i class="fas fa-box display-1 opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filter and Search Section -->
<section class="filter-section bg-light py-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <form method="GET" class="search-form">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" 
                               placeholder="Cari produk..." value="<?php echo htmlspecialchars($search_query); ?>">
                        <input type="hidden" name="page" value="products">
                        <?php if ($category_filter): ?>
                            <input type="hidden" name="category" value="<?php echo $category_filter; ?>">
                        <?php endif; ?>
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            <div class="col-lg-6">
                <div class="filter-buttons text-lg-end mt-3 mt-lg-0">
                    <a href="index.php?page=products" 
                       class="btn btn-outline-primary btn-sm <?php echo empty($category_filter) ? 'active' : ''; ?>">
                        Semua Produk
                    </a>
                    <a href="index.php?page=products&category=air_isi_ulang" 
                       class="btn btn-outline-primary btn-sm <?php echo $category_filter == 'air_isi_ulang' ? 'active' : ''; ?>">
                        Air Isi Ulang
                    </a>
                    <a href="index.php?page=products&category=air_kemasan" 
                       class="btn btn-outline-primary btn-sm <?php echo $category_filter == 'air_kemasan' ? 'active' : ''; ?>">
                        Air Kemasan
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Products Grid Section -->
<section class="products-section section">
    <div class="container">
        <?php if (!empty($products)): ?>
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3>
                            <?php if ($search_query): ?>
                                Hasil pencarian "<?php echo htmlspecialchars($search_query); ?>"
                            <?php elseif ($category_filter == 'air_isi_ulang'): ?>
                                Air Isi Ulang
                            <?php elseif ($category_filter == 'air_kemasan'): ?>
                                Air Mineral Kemasan
                            <?php else: ?>
                                Semua Produk
                            <?php endif; ?>
                        </h3>
                        <span class="badge bg-primary"><?php echo count($products); ?> produk</span>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <?php foreach ($products as $index => $product): ?>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?php echo ($index % 3 + 1) * 100; ?>">
                    <div class="product-card h-100">
                        <div class="product-image-container">
                            <img src="assets/images/products/<?php echo $product['image']; ?>" 
                                 alt="<?php echo $product['name']; ?>" 
                                 class="product-image">
                            <?php if ($product['is_featured']): ?>
                                <div class="product-badge featured">Unggulan</div>
                            <?php endif; ?>
                            <div class="product-overlay">
                                <button class="btn btn-light btn-sm" onclick="viewProduct(<?php echo $product['id']; ?>)">
                                    <i class="fas fa-eye me-1"></i>
                                    Lihat Detail
                                </button>
                            </div>
                        </div>
                        
                        <div class="product-info">
                            <div class="product-category">
                                <span class="badge bg-secondary">
                                    <?php echo $product['category'] == 'air_isi_ulang' ? 'Air Isi Ulang' : 'Air Kemasan'; ?>
                                </span>
                            </div>
                            <h5 class="product-name"><?php echo $product['name']; ?></h5>
                            <p class="product-description text-muted">
                                <?php echo substr($product['description'], 0, 120) . '...'; ?>
                            </p>
                            <div class="product-price-container">
                                <div class="product-price"><?php echo formatCurrency($product['price']); ?></div>
                                <small class="text-muted">
                                    <?php echo $product['category'] == 'air_isi_ulang' ? 'per galon' : 'per botol'; ?>
                                </small>
                            </div>
                        </div>
                        
                        <div class="product-actions">
                            <div class="row g-2">
                                <div class="col-6">
                                    <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>?text=Halo, saya ingin memesan <?php echo urlencode($product['name']); ?> dengan harga <?php echo urlencode(formatCurrency($product['price'])); ?>" 
                                       class="btn btn-primary btn-sm w-100" target="_blank">
                                        <i class="fab fa-whatsapp me-1"></i>
                                        Pesan
                                    </a>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-outline-primary btn-sm w-100" 
                                            onclick="viewProduct(<?php echo $product['id']; ?>)">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Detail
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-12">
                    <div class="empty-state text-center py-5">
                        <i class="fas fa-search display-1 text-muted mb-4"></i>
                        <h3 class="text-muted">Produk Tidak Ditemukan</h3>
                        <p class="text-muted mb-4">
                            <?php if ($search_query): ?>
                                Tidak ada produk yang sesuai dengan pencarian "<?php echo htmlspecialchars($search_query); ?>".
                            <?php else: ?>
                                Belum ada produk dalam kategori ini.
                            <?php endif; ?>
                        </p>
                        <a href="index.php?page=products" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Lihat Semua Produk
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- CTA Section -->
<section class="section bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8" data-aos="fade-right">
                <h3 class="mb-3">Butuh Bantuan Memilih Produk?</h3>
                <p class="text-muted mb-0">
                    Tim kami siap membantu Anda memilih produk yang tepat sesuai kebutuhan. 
                    Hubungi kami untuk konsultasi gratis!
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>?text=Halo, saya butuh bantuan memilih produk air yang tepat" 
                   class="btn btn-primary btn-lg" target="_blank">
                    <i class="fab fa-whatsapp me-2"></i>
                    Konsultasi Gratis
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Product Detail Modal -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detail Produk</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="productModalBody">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

/* Filter Section Styles */
.filter-section {
    border-bottom: 1px solid var(--border-color);
}

.search-form .form-control {
    border-radius: 50px 0 0 50px;
    border-right: none;
}

.search-form .btn {
    border-radius: 0 50px 50px 0;
}

.filter-buttons .btn {
    margin-left: 0.5rem;
    border-radius: 50px;
}

.filter-buttons .btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Product Card Styles */
.product-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.product-image-container {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--accent-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 2;
}

.product-badge.featured {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-info {
    padding: 1.5rem;
    flex-grow: 1;
}

.product-category {
    margin-bottom: 0.75rem;
}

.product-name {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-dark);
}

.product-description {
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.product-price-container {
    margin-bottom: 1rem;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.product-actions {
    padding: 0 1.5rem 1.5rem;
}

/* Empty State Styles */
.empty-state {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Modal Styles */
.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header .display-4 {
        font-size: 2rem;
    }

    .filter-buttons {
        text-align: center !important;
    }

    .filter-buttons .btn {
        margin: 0.25rem;
        font-size: 0.875rem;
    }

    .product-image-container {
        height: 200px;
    }

    .product-info {
        padding: 1rem;
    }

    .product-actions {
        padding: 0 1rem 1rem;
    }
}

/* Animation for product cards */
.product-card {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
// Product detail modal functionality
function viewProduct(productId) {
    // Show loading in modal
    const modalBody = document.getElementById('productModalBody');
    modalBody.innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
            <p>Memuat detail produk...</p>
        </div>
    `;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();

    // Fetch product details
    fetch(`ajax/get_product.php?id=${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const product = data.product;
                modalBody.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <img src="assets/images/products/${product.image}"
                                 alt="${product.name}"
                                 class="img-fluid rounded">
                        </div>
                        <div class="col-md-6">
                            <div class="product-detail-info">
                                <div class="mb-3">
                                    <span class="badge bg-secondary">${product.category == 'air_isi_ulang' ? 'Air Isi Ulang' : 'Air Kemasan'}</span>
                                    ${product.is_featured ? '<span class="badge bg-warning ms-2">Unggulan</span>' : ''}
                                </div>
                                <h4 class="mb-3">${product.name}</h4>
                                <p class="text-muted mb-3">${product.description}</p>
                                <div class="price-info mb-4">
                                    <div class="h3 text-primary mb-1">${formatCurrency(product.price)}</div>
                                    <small class="text-muted">${product.category == 'air_isi_ulang' ? 'per galon' : 'per botol'}</small>
                                </div>
                                <div class="d-grid gap-2">
                                    <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>?text=Halo, saya ingin memesan ${encodeURIComponent(product.name)} dengan harga ${encodeURIComponent(formatCurrency(product.price))}"
                                       class="btn btn-primary btn-lg" target="_blank">
                                        <i class="fab fa-whatsapp me-2"></i>
                                        Pesan Sekarang
                                    </a>
                                    <a href="tel:<?php echo $company_info['company_phone']; ?>"
                                       class="btn btn-outline-primary">
                                        <i class="fas fa-phone me-2"></i>
                                        Telepon Langsung
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                modalBody.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                        <p>Gagal memuat detail produk. Silakan coba lagi.</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            modalBody.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                    <p>Terjadi kesalahan. Silakan coba lagi.</p>
                </div>
            `;
        });
}

// Format currency function for JavaScript
function formatCurrency(amount) {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
}

// Search form enhancement
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.querySelector('.search-form');
    const searchInput = searchForm.querySelector('input[name="search"]');

    // Auto-submit on Enter key
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchForm.submit();
        }
    });

    // Clear search functionality
    if (searchInput.value) {
        const clearBtn = document.createElement('button');
        clearBtn.type = 'button';
        clearBtn.className = 'btn btn-outline-secondary';
        clearBtn.innerHTML = '<i class="fas fa-times"></i>';
        clearBtn.onclick = function() {
            searchInput.value = '';
            searchForm.submit();
        };

        const inputGroup = searchInput.parentElement;
        inputGroup.appendChild(clearBtn);
    }
});
</script>
