<?php
// Sample data insertion script
require_once '../config/database.php';

// Insert sample products
$sample_products = [
    [
        'name' => 'Air Isi Ulang Premium',
        'description' => 'Air isi ulang dengan kualitas premium, telah melalui proses penyaringan berlapis dan sterilisasi UV untuk memastikan kemurnian dan kesegaran.',
        'price' => 3000,
        'image' => 'air-isi-ulang-premium.jpg',
        'category' => 'air_isi_ulang',
        'is_featured' => 1
    ],
    [
        'name' => 'Air Isi Ulang Reguler',
        'description' => 'Air isi ulang dengan kualitas standar yang tetap terjamin kebersihannya, cocok untuk kebutuhan sehari-hari.',
        'price' => 2000,
        'image' => 'air-isi-ulang-reguler.jpg',
        'category' => 'air_isi_ulang',
        'is_featured' => 0
    ],
    [
        'name' => 'Air Mineral Kemasan 600ml',
        'description' => 'Air mineral dalam kemasan botol 600ml, praktis dibawa kemana-mana dengan kualitas terjamin.',
        'price' => 2500,
        'image' => 'air-mineral-600ml.jpg',
        'category' => 'air_kemasan',
        'is_featured' => 1
    ],
    [
        'name' => 'Air Mineral Kemasan 1.5L',
        'description' => 'Air mineral dalam kemasan botol 1.5 liter, ideal untuk keluarga dan acara-acara khusus.',
        'price' => 4000,
        'image' => 'air-mineral-1500ml.jpg',
        'category' => 'air_kemasan',
        'is_featured' => 0
    ],
    [
        'name' => 'Paket Air Isi Ulang Bulanan',
        'description' => 'Paket berlangganan air isi ulang untuk satu bulan, hemat dan praktis dengan pengiriman rutin.',
        'price' => 75000,
        'image' => 'paket-bulanan.jpg',
        'category' => 'air_isi_ulang',
        'is_featured' => 1
    ]
];

$stmt = $pdo->prepare("INSERT INTO products (name, description, price, image, category, is_featured) VALUES (?, ?, ?, ?, ?, ?)");
foreach ($sample_products as $product) {
    $stmt->execute([
        $product['name'],
        $product['description'],
        $product['price'],
        $product['image'],
        $product['category'],
        $product['is_featured']
    ]);
}

// Insert sample testimonials
$sample_testimonials = [
    [
        'customer_name' => 'Ibu Sari Wijaya',
        'customer_photo' => 'customer-1.jpg',
        'rating' => 5,
        'message' => 'Pelayanan Agen Air Fazza sangat memuaskan! Air yang diberikan selalu segar dan bersih. Pengiriman juga selalu tepat waktu. Sangat recommended!'
    ],
    [
        'customer_name' => 'Bapak Ahmad Rizki',
        'customer_photo' => 'customer-2.jpg',
        'rating' => 5,
        'message' => 'Sudah berlangganan air isi ulang di sini selama 2 tahun. Kualitas air konsisten bagus dan harga sangat terjangkau. Terima kasih Agen Air Fazza!'
    ],
    [
        'customer_name' => 'Ibu Maya Sari',
        'customer_photo' => 'customer-3.jpg',
        'rating' => 4,
        'message' => 'Air mineral kemasannya praktis dan rasanya segar. Cocok untuk dibawa traveling. Pelayanan ramah dan profesional.'
    ],
    [
        'customer_name' => 'Bapak Dedi Kurniawan',
        'customer_photo' => 'customer-4.jpg',
        'rating' => 5,
        'message' => 'Agen Air Fazza adalah pilihan terbaik untuk kebutuhan air bersih keluarga. Kualitas terjamin dan harga bersahabat!'
    ]
];

$stmt = $pdo->prepare("INSERT INTO testimonials (customer_name, customer_photo, rating, message) VALUES (?, ?, ?, ?)");
foreach ($sample_testimonials as $testimonial) {
    $stmt->execute([
        $testimonial['customer_name'],
        $testimonial['customer_photo'],
        $testimonial['rating'],
        $testimonial['message']
    ]);
}

// Insert sample gallery images
$sample_gallery = [
    [
        'title' => 'Proses Pengisian Air Isi Ulang',
        'description' => 'Proses pengisian air isi ulang dengan teknologi modern dan higienis',
        'image' => 'gallery-1.jpg',
        'category' => 'kegiatan'
    ],
    [
        'title' => 'Fasilitas Penyaringan Air',
        'description' => 'Sistem penyaringan air berlapis untuk menjamin kualitas',
        'image' => 'gallery-2.jpg',
        'category' => 'fasilitas'
    ],
    [
        'title' => 'Produk Air Mineral Kemasan',
        'description' => 'Berbagai varian air mineral kemasan yang tersedia',
        'image' => 'gallery-3.jpg',
        'category' => 'produk'
    ],
    [
        'title' => 'Tim Pengiriman Profesional',
        'description' => 'Tim pengiriman yang siap melayani dengan profesional',
        'image' => 'gallery-4.jpg',
        'category' => 'kegiatan'
    ],
    [
        'title' => 'Armada Distribusi',
        'description' => 'Armada distribusi yang siap melayani wilayah sekitar',
        'image' => 'gallery-5.jpg',
        'category' => 'fasilitas'
    ],
    [
        'title' => 'Kualitas Air Terjamin',
        'description' => 'Pengujian kualitas air secara berkala untuk menjamin kemurnian',
        'image' => 'gallery-6.jpg',
        'category' => 'kegiatan'
    ]
];

$stmt = $pdo->prepare("INSERT INTO gallery (title, description, image, category) VALUES (?, ?, ?, ?)");
foreach ($sample_gallery as $gallery) {
    $stmt->execute([
        $gallery['title'],
        $gallery['description'],
        $gallery['image'],
        $gallery['category']
    ]);
}

echo "Sample data berhasil dimasukkan ke database!";
?>
