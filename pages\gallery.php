<?php
// Get gallery images and testimonials
$gallery_images = getGalleryImages();
$testimonials = getTestimonials();
$company_info = getCompanyInfo();

// Get filter parameters
$gallery_filter = isset($_GET['filter']) ? $_GET['filter'] : '';
if ($gallery_filter) {
    $gallery_images = getGalleryImages($gallery_filter);
}
?>

<!-- Page Header -->
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row align-items-center py-5">
            <div class="col-lg-8" data-aos="fade-right">
                <h1 class="display-4 fw-bold mb-3">Galeri & Testimoni</h1>
                <p class="lead mb-0">
                    Lihat dokumentasi kegiatan kami dan dengarkan pengalaman pelanggan yang telah 
                    merasakan kualitas produk dan layanan Agen Air Fazza.
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <div class="page-header-icon">
                    <i class="fas fa-images display-1 opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Gallery Section -->
<section id="gallery" class="section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">Galeri Foto</h2>
                <p class="lead text-muted">
                    Dokumentasi kegiatan, fasilitas, dan produk kami dalam melayani pelanggan
                </p>
            </div>
        </div>
        
        <!-- Gallery Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="gallery-filter text-center">
                    <button class="filter-btn <?php echo empty($gallery_filter) ? 'active' : ''; ?>" 
                            data-filter="all" onclick="filterGallery('all')">
                        Semua Foto
                    </button>
                    <button class="filter-btn <?php echo $gallery_filter == 'kegiatan' ? 'active' : ''; ?>" 
                            data-filter="kegiatan" onclick="filterGallery('kegiatan')">
                        Kegiatan
                    </button>
                    <button class="filter-btn <?php echo $gallery_filter == 'produk' ? 'active' : ''; ?>" 
                            data-filter="produk" onclick="filterGallery('produk')">
                        Produk
                    </button>
                    <button class="filter-btn <?php echo $gallery_filter == 'fasilitas' ? 'active' : ''; ?>" 
                            data-filter="fasilitas" onclick="filterGallery('fasilitas')">
                        Fasilitas
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Gallery Grid -->
        <?php if (!empty($gallery_images)): ?>
        <div class="gallery-grid" id="galleryGrid">
            <?php foreach ($gallery_images as $index => $image): ?>
            <div class="gallery-item" data-category="<?php echo $image['category']; ?>" 
                 data-aos="fade-up" data-aos-delay="<?php echo ($index % 6 + 1) * 100; ?>">
                <div class="gallery-card">
                    <div class="gallery-image-container">
                        <img src="assets/images/gallery/<?php echo $image['image']; ?>" 
                             alt="<?php echo $image['title']; ?>" 
                             class="gallery-image"
                             loading="lazy">
                        <div class="gallery-overlay">
                            <div class="gallery-overlay-content">
                                <button class="gallery-view-btn" onclick="openLightbox(<?php echo $index; ?>)">
                                    <i class="fas fa-search-plus"></i>
                                </button>
                                <div class="gallery-category-badge">
                                    <?php 
                                    $category_labels = [
                                        'kegiatan' => 'Kegiatan',
                                        'produk' => 'Produk', 
                                        'fasilitas' => 'Fasilitas'
                                    ];
                                    echo $category_labels[$image['category']];
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="gallery-info">
                        <h6 class="gallery-title"><?php echo $image['title']; ?></h6>
                        <p class="gallery-description"><?php echo $image['description']; ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php else: ?>
        <div class="empty-gallery text-center py-5">
            <i class="fas fa-images display-1 text-muted mb-4"></i>
            <h3 class="text-muted">Belum Ada Foto</h3>
            <p class="text-muted">
                <?php if ($gallery_filter): ?>
                    Belum ada foto dalam kategori "<?php echo ucfirst($gallery_filter); ?>".
                <?php else: ?>
                    Galeri foto sedang dalam proses pengembangan.
                <?php endif; ?>
            </p>
            <?php if ($gallery_filter): ?>
            <button class="btn btn-primary" onclick="filterGallery('all')">
                <i class="fas fa-arrow-left me-2"></i>
                Lihat Semua Foto
            </button>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Testimonials Section -->
<section id="testimonials" class="section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">Testimoni Pelanggan</h2>
                <p class="lead text-muted">
                    Kepuasan dan kepercayaan pelanggan adalah motivasi terbesar kami
                </p>
            </div>
        </div>
        
        <?php if (!empty($testimonials)): ?>
        <div class="testimonials-container">
            <div class="row">
                <?php foreach ($testimonials as $index => $testimonial): ?>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?php echo ($index % 3 + 1) * 100; ?>">
                    <div class="testimonial-card">
                        <div class="testimonial-header">
                            <div class="testimonial-avatar">
                                <img src="assets/images/testimonials/<?php echo $testimonial['customer_photo']; ?>" 
                                     alt="<?php echo $testimonial['customer_name']; ?>"
                                     onerror="this.src='assets/images/default-avatar.jpg'">
                            </div>
                            <div class="testimonial-info">
                                <h6 class="testimonial-name"><?php echo $testimonial['customer_name']; ?></h6>
                                <div class="testimonial-rating">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star<?php echo $i <= $testimonial['rating'] ? '' : '-o'; ?>"></i>
                                    <?php endfor; ?>
                                </div>
                            </div>
                        </div>
                        <div class="testimonial-content">
                            <div class="testimonial-quote">
                                <i class="fas fa-quote-left"></i>
                            </div>
                            <p class="testimonial-message"><?php echo $testimonial['message']; ?></p>
                            <div class="testimonial-date">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo formatDate($testimonial['created_at']); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php else: ?>
        <div class="empty-testimonials text-center py-5">
            <i class="fas fa-comments display-1 text-muted mb-4"></i>
            <h3 class="text-muted">Belum Ada Testimoni</h3>
            <p class="text-muted">Testimoni pelanggan akan ditampilkan di sini.</p>
        </div>
        <?php endif; ?>
        
        <!-- Add Testimonial CTA -->
        <div class="text-center mt-5" data-aos="fade-up">
            <div class="testimonial-cta">
                <h4 class="mb-3">Sudah Merasakan Pelayanan Kami?</h4>
                <p class="text-muted mb-4">
                    Bagikan pengalaman Anda dan bantu calon pelanggan lain untuk mengenal kualitas layanan kami.
                </p>
                <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>?text=Halo, saya ingin memberikan testimoni untuk layanan Agen Air Fazza" 
                   class="btn btn-primary btn-lg" target="_blank">
                    <i class="fas fa-comment-dots me-2"></i>
                    Berikan Testimoni
                </a>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8" data-aos="fade-right">
                <h2 class="mb-3">Tertarik dengan Produk dan Layanan Kami?</h2>
                <p class="lead mb-0">
                    Bergabunglah dengan ratusan pelanggan yang telah merasakan kualitas air bersih 
                    dan pelayanan terbaik dari Agen Air Fazza.
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <div class="cta-buttons">
                    <a href="index.php?page=products" class="btn btn-light btn-lg mb-2 me-2">
                        <i class="fas fa-box me-2"></i>
                        Lihat Produk
                    </a>
                    <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>?text=Halo, saya tertarik dengan produk Agen Air Fazza" 
                       class="btn btn-outline-light btn-lg mb-2" target="_blank">
                        <i class="fab fa-whatsapp me-2"></i>
                        Hubungi Kami
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Lightbox Modal -->
<div class="lightbox-modal" id="lightboxModal">
    <div class="lightbox-content">
        <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
        <div class="lightbox-image-container">
            <img id="lightboxImage" src="" alt="">
            <div class="lightbox-info">
                <h5 id="lightboxTitle"></h5>
                <p id="lightboxDescription"></p>
            </div>
        </div>
        <div class="lightbox-navigation">
            <button class="lightbox-prev" onclick="previousImage()">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="lightbox-next" onclick="nextImage()">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>

<style>
/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

/* Gallery Filter Styles */
.gallery-filter {
    margin-bottom: 2rem;
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    margin: 0 0.5rem 0.5rem;
    border-radius: 50px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

/* Gallery Grid Styles */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.gallery-item {
    opacity: 1;
    transform: scale(1);
    transition: all 0.5s ease;
}

.gallery-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.gallery-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
}

.gallery-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.gallery-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-card:hover .gallery-image {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay-content {
    text-align: center;
    position: relative;
}

.gallery-view-btn {
    background: white;
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    color: var(--primary-color);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.gallery-view-btn:hover {
    transform: scale(1.1);
    background: var(--primary-color);
    color: white;
}

.gallery-category-badge {
    position: absolute;
    top: -80px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
}

.gallery-info {
    padding: 1.5rem;
}

.gallery-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.gallery-description {
    color: var(--text-light);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0;
}

/* Testimonial Styles */
.testimonial-card {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
}

.testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 1rem 1rem 0 0;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.testimonial-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 1rem;
    flex-shrink: 0;
}

.testimonial-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-info {
    flex-grow: 1;
}

.testimonial-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.testimonial-rating {
    color: #fbbf24;
    font-size: 0.875rem;
}

.testimonial-content {
    position: relative;
}

.testimonial-quote {
    position: absolute;
    top: -10px;
    left: -10px;
    font-size: 2rem;
    color: var(--primary-color);
    opacity: 0.3;
}

.testimonial-message {
    font-style: italic;
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--text-light);
    padding-left: 1rem;
}

.testimonial-date {
    text-align: right;
}

.testimonial-cta {
    background: white;
    padding: 2.5rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* Lightbox Styles */
.lightbox-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    animation: fadeIn 0.3s ease;
}

.lightbox-content {
    position: relative;
    width: 90%;
    max-width: 800px;
    margin: 2% auto;
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.lightbox-close {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 2rem;
    color: white;
    cursor: pointer;
    z-index: 10001;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.lightbox-close:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.lightbox-image-container {
    position: relative;
}

.lightbox-image-container img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.lightbox-info {
    padding: 1.5rem;
}

.lightbox-info h5 {
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.lightbox-info p {
    color: var(--text-light);
    margin-bottom: 0;
}

.lightbox-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 1rem;
    pointer-events: none;
}

.lightbox-prev,
.lightbox-next {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    pointer-events: all;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

/* Empty States */
.empty-gallery,
.empty-testimonials {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header .display-4 {
        font-size: 2rem;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .gallery-image-container {
        height: 200px;
    }

    .filter-btn {
        margin: 0.25rem;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .testimonial-card {
        padding: 1.5rem;
    }

    .testimonial-header {
        flex-direction: column;
        text-align: center;
    }

    .testimonial-avatar {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .lightbox-content {
        width: 95%;
        margin: 5% auto;
    }

    .lightbox-image-container img {
        height: 250px;
    }

    .testimonial-cta {
        padding: 2rem 1.5rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.gallery-item,
.testimonial-card {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
// Gallery data for lightbox
let galleryData = <?php echo json_encode($gallery_images); ?>;
let currentImageIndex = 0;

// Gallery filter functionality
function filterGallery(category) {
    const items = document.querySelectorAll('.gallery-item');
    const filterBtns = document.querySelectorAll('.filter-btn');

    // Update active filter button
    filterBtns.forEach(btn => btn.classList.remove('active'));
    document.querySelector(`[data-filter="${category}"]`).classList.add('active');

    // Filter items
    items.forEach(item => {
        const itemCategory = item.getAttribute('data-category');

        if (category === 'all' || itemCategory === category) {
            item.classList.remove('hidden');
            setTimeout(() => {
                item.style.display = 'block';
            }, 50);
        } else {
            item.classList.add('hidden');
            setTimeout(() => {
                item.style.display = 'none';
            }, 500);
        }
    });

    // Update URL without page reload
    const url = new URL(window.location);
    if (category === 'all') {
        url.searchParams.delete('filter');
    } else {
        url.searchParams.set('filter', category);
    }
    window.history.pushState({}, '', url);
}

// Lightbox functionality
function openLightbox(index) {
    currentImageIndex = index;
    const modal = document.getElementById('lightboxModal');
    const image = document.getElementById('lightboxImage');
    const title = document.getElementById('lightboxTitle');
    const description = document.getElementById('lightboxDescription');

    if (galleryData[index]) {
        const item = galleryData[index];
        image.src = `assets/images/gallery/${item.image}`;
        image.alt = item.title;
        title.textContent = item.title;
        description.textContent = item.description;

        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';

        // Add keyboard event listener
        document.addEventListener('keydown', handleKeyboard);
    }
}

function closeLightbox() {
    const modal = document.getElementById('lightboxModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';

    // Remove keyboard event listener
    document.removeEventListener('keydown', handleKeyboard);
}

function previousImage() {
    currentImageIndex = currentImageIndex > 0 ? currentImageIndex - 1 : galleryData.length - 1;
    updateLightboxImage();
}

function nextImage() {
    currentImageIndex = currentImageIndex < galleryData.length - 1 ? currentImageIndex + 1 : 0;
    updateLightboxImage();
}

function updateLightboxImage() {
    const image = document.getElementById('lightboxImage');
    const title = document.getElementById('lightboxTitle');
    const description = document.getElementById('lightboxDescription');

    if (galleryData[currentImageIndex]) {
        const item = galleryData[currentImageIndex];
        image.src = `assets/images/gallery/${item.image}`;
        image.alt = item.title;
        title.textContent = item.title;
        description.textContent = item.description;
    }
}

function handleKeyboard(e) {
    switch(e.key) {
        case 'Escape':
            closeLightbox();
            break;
        case 'ArrowLeft':
            previousImage();
            break;
        case 'ArrowRight':
            nextImage();
            break;
    }
}

// Close lightbox when clicking outside
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('lightboxModal');

    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeLightbox();
        }
    });

    // Smooth scroll for anchor links
    const galleryLink = document.querySelector('a[href="#gallery"]');
    const testimonialsLink = document.querySelector('a[href="#testimonials"]');

    if (galleryLink) {
        galleryLink.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('gallery').scrollIntoView({
                behavior: 'smooth'
            });
        });
    }

    if (testimonialsLink) {
        testimonialsLink.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('testimonials').scrollIntoView({
                behavior: 'smooth'
            });
        });
    }

    // Initialize masonry-like layout for gallery
    initGalleryLayout();

    // Lazy loading for images
    initLazyLoading();
});

// Gallery layout initialization
function initGalleryLayout() {
    const grid = document.getElementById('galleryGrid');
    if (!grid) return;

    // Add stagger animation delay
    const items = grid.querySelectorAll('.gallery-item');
    items.forEach((item, index) => {
        item.style.animationDelay = `${(index % 6) * 0.1}s`;
    });
}

// Lazy loading for gallery images
function initLazyLoading() {
    const images = document.querySelectorAll('.gallery-image[loading="lazy"]');

    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.classList.add('loaded');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => {
        imageObserver.observe(img);

        // Add loading placeholder
        img.addEventListener('load', function() {
            this.style.opacity = '1';
        });

        img.addEventListener('error', function() {
            this.src = 'assets/images/placeholder-gallery.jpg';
            this.alt = 'Image not available';
        });
    });
}

// Testimonial slider functionality (if needed)
function initTestimonialSlider() {
    const testimonials = document.querySelectorAll('.testimonial-card');

    if (testimonials.length > 3) {
        // Add slider functionality for mobile
        // This can be expanded based on requirements
    }
}

// Search functionality for testimonials
function searchTestimonials(query) {
    const testimonials = document.querySelectorAll('.testimonial-card');
    const searchQuery = query.toLowerCase();

    testimonials.forEach(testimonial => {
        const name = testimonial.querySelector('.testimonial-name').textContent.toLowerCase();
        const message = testimonial.querySelector('.testimonial-message').textContent.toLowerCase();

        if (name.includes(searchQuery) || message.includes(searchQuery)) {
            testimonial.style.display = 'block';
        } else {
            testimonial.style.display = 'none';
        }
    });
}

// Share functionality
function shareGalleryItem(index) {
    if (navigator.share && galleryData[index]) {
        const item = galleryData[index];
        navigator.share({
            title: item.title,
            text: item.description,
            url: window.location.href
        });
    }
}

// Print functionality
function printGallery() {
    window.print();
}
</script>
