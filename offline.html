<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Agen Air Fazza</title>
    
    <!-- Mobile-specific meta tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#2563eb">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #2563eb, #06b6d4);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }
        
        .offline-container {
            max-width: 500px;
            width: 100%;
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            min-width: 200px;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.9);
            color: #2563eb;
            border-color: transparent;
        }
        
        .btn-primary:hover {
            background: white;
            color: #1d4ed8;
        }
        
        .offline-tips {
            margin-top: 3rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
            backdrop-filter: blur(10px);
        }
        
        .offline-tips h3 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        
        .offline-tips ul {
            list-style: none;
            text-align: left;
        }
        
        .offline-tips li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }
        
        .offline-tips li::before {
            content: '💡';
            position: absolute;
            left: 0;
        }
        
        .connection-status {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
        }
        
        .connection-status.online {
            background: rgba(16, 185, 129, 0.2);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        
        .connection-status.offline {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .offline-title {
                font-size: 1.5rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
            
            .btn {
                padding: 0.75rem 1.5rem;
                min-width: 180px;
            }
        }
        
        /* PWA-specific styles */
        @media (display-mode: standalone) {
            body {
                padding-top: env(safe-area-inset-top);
                padding-bottom: env(safe-area-inset-bottom);
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            📶❌
        </div>
        
        <h1 class="offline-title">Tidak Ada Koneksi Internet</h1>
        
        <p class="offline-message">
            Maaf, Anda sedang offline. Beberapa fitur mungkin tidak tersedia, 
            tetapi Anda masih dapat menjelajahi konten yang telah dimuat sebelumnya.
        </p>
        
        <div class="offline-actions">
            <button onclick="checkConnection()" class="btn btn-primary" id="retryBtn">
                <span id="retryText">Coba Lagi</span>
            </button>
            
            <a href="/" class="btn" onclick="goHome()">
                Kembali ke Beranda
            </a>
            
            <button onclick="showCachedPages()" class="btn">
                Lihat Halaman Tersimpan
            </button>
        </div>
        
        <div id="connectionStatus" class="connection-status offline">
            Status: Offline
        </div>
        
        <div class="offline-tips">
            <h3>Tips saat Offline:</h3>
            <ul>
                <li>Periksa koneksi WiFi atau data seluler Anda</li>
                <li>Coba pindah ke lokasi dengan sinyal yang lebih baik</li>
                <li>Restart router WiFi jika menggunakan WiFi</li>
                <li>Beberapa halaman mungkin masih dapat diakses dari cache</li>
                <li>Formulir yang Anda isi akan disimpan dan dikirim saat online</li>
            </ul>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const retryBtn = document.getElementById('retryBtn');
            
            if (navigator.onLine) {
                statusElement.textContent = 'Status: Online - Koneksi tersedia';
                statusElement.className = 'connection-status online';
                retryBtn.style.display = 'none';
                
                // Auto redirect to home when back online
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            } else {
                statusElement.textContent = 'Status: Offline - Tidak ada koneksi';
                statusElement.className = 'connection-status offline';
                retryBtn.style.display = 'inline-block';
            }
        }
        
        // Check connection manually
        function checkConnection() {
            const retryBtn = document.getElementById('retryBtn');
            const retryText = document.getElementById('retryText');
            
            retryText.innerHTML = '<span class="loading-spinner"></span>Memeriksa...';
            retryBtn.disabled = true;
            
            // Simulate checking connection
            setTimeout(() => {
                updateConnectionStatus();
                retryText.textContent = 'Coba Lagi';
                retryBtn.disabled = false;
                
                if (navigator.onLine) {
                    // Try to fetch a small resource to verify connection
                    fetch('/', { method: 'HEAD', cache: 'no-cache' })
                        .then(() => {
                            window.location.href = '/';
                        })
                        .catch(() => {
                            // Still offline
                            updateConnectionStatus();
                        });
                }
            }, 2000);
        }
        
        // Go to home page
        function goHome() {
            window.location.href = '/';
        }
        
        // Show cached pages
        function showCachedPages() {
            const cachedPages = [
                { name: 'Beranda', url: '/' },
                { name: 'Produk', url: '/index.php?page=products' },
                { name: 'Profil Usaha', url: '/index.php?page=profile' },
                { name: 'Galeri & Testimoni', url: '/index.php?page=gallery' },
                { name: 'Kontak Kami', url: '/index.php?page=contact' }
            ];
            
            let pagesList = 'Halaman yang mungkin tersedia offline:\n\n';
            cachedPages.forEach((page, index) => {
                pagesList += `${index + 1}. ${page.name}\n`;
            });
            
            alert(pagesList + '\nKlik "Kembali ke Beranda" untuk mencoba mengakses halaman tersebut.');
        }
        
        // Listen for online/offline events
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodic connection check
        setInterval(updateConnectionStatus, 30000); // Check every 30 seconds
        
        // Service Worker registration check
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then((registration) => {
                console.log('Service Worker is ready');
                
                // Listen for service worker updates
                registration.addEventListener('updatefound', () => {
                    console.log('Service Worker update found');
                });
            });
        }
        
        // Handle back button
        window.addEventListener('popstate', (event) => {
            if (navigator.onLine) {
                window.location.href = '/';
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            if (event.key === 'r' || event.key === 'R') {
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    checkConnection();
                }
            }
            
            if (event.key === 'h' || event.key === 'H') {
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    goHome();
                }
            }
        });
        
        // PWA install prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // Show install button if not already installed
            if (!window.matchMedia('(display-mode: standalone)').matches) {
                const installBtn = document.createElement('button');
                installBtn.textContent = 'Install App';
                installBtn.className = 'btn';
                installBtn.onclick = () => {
                    deferredPrompt.prompt();
                    deferredPrompt.userChoice.then((choiceResult) => {
                        if (choiceResult.outcome === 'accepted') {
                            console.log('User accepted the install prompt');
                        }
                        deferredPrompt = null;
                    });
                };
                
                document.querySelector('.offline-actions').appendChild(installBtn);
            }
        });
    </script>
</body>
</html>
