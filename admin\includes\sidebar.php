<?php
// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF'], '.php');
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
    <div class="sidebar-sticky pt-3">
        <!-- Main Navigation -->
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'dashboard' ? 'active' : ''; ?>" 
                   href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                </a>
            </li>
        </ul>
        
        <!-- Content Management -->
        <h6 class="sidebar-heading">
            Manajemen Konten
        </h6>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'products' ? 'active' : ''; ?>" 
                   href="products.php">
                    <i class="fas fa-box me-2"></i>
                    Produk
                    <?php
                    // Get product count
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
                        $product_count = $stmt->fetch()['count'];
                        if ($product_count > 0) {
                            echo '<span class="badge bg-primary ms-auto">' . $product_count . '</span>';
                        }
                    } catch (Exception $e) {
                        // Ignore error
                    }
                    ?>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'gallery' ? 'active' : ''; ?>" 
                   href="gallery.php">
                    <i class="fas fa-images me-2"></i>
                    Galeri
                    <?php
                    // Get gallery count
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) as count FROM gallery WHERE status = 'active'");
                        $gallery_count = $stmt->fetch()['count'];
                        if ($gallery_count > 0) {
                            echo '<span class="badge bg-success ms-auto">' . $gallery_count . '</span>';
                        }
                    } catch (Exception $e) {
                        // Ignore error
                    }
                    ?>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'testimonials' ? 'active' : ''; ?>" 
                   href="testimonials.php">
                    <i class="fas fa-comments me-2"></i>
                    Testimoni
                    <?php
                    // Get testimonial count
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) as count FROM testimonials WHERE status = 'active'");
                        $testimonial_count = $stmt->fetch()['count'];
                        if ($testimonial_count > 0) {
                            echo '<span class="badge bg-info ms-auto">' . $testimonial_count . '</span>';
                        }
                    } catch (Exception $e) {
                        // Ignore error
                    }
                    ?>
                </a>
            </li>
        </ul>
        
        <!-- Communication -->
        <h6 class="sidebar-heading">
            Komunikasi
        </h6>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'messages' ? 'active' : ''; ?>" 
                   href="messages.php">
                    <i class="fas fa-envelope me-2"></i>
                    Pesan Kontak
                    <?php
                    // Get unread message count
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) as count FROM contact_messages WHERE status = 'unread'");
                        $unread_count = $stmt->fetch()['count'];
                        if ($unread_count > 0) {
                            echo '<span class="badge bg-warning ms-auto">' . $unread_count . '</span>';
                        }
                    } catch (Exception $e) {
                        // Ignore error
                    }
                    ?>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'newsletter' ? 'active' : ''; ?>" 
                   href="newsletter.php">
                    <i class="fas fa-paper-plane me-2"></i>
                    Newsletter
                </a>
            </li>
        </ul>
        
        <!-- Settings -->
        <h6 class="sidebar-heading">
            Pengaturan
        </h6>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'company' ? 'active' : ''; ?>" 
                   href="company.php">
                    <i class="fas fa-building me-2"></i>
                    Info Perusahaan
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'users' ? 'active' : ''; ?>" 
                   href="users.php">
                    <i class="fas fa-users me-2"></i>
                    Pengguna Admin
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'settings' ? 'active' : ''; ?>" 
                   href="settings.php">
                    <i class="fas fa-cog me-2"></i>
                    Pengaturan Sistem
                </a>
            </li>
        </ul>
        
        <!-- Tools -->
        <h6 class="sidebar-heading">
            Tools & Utilitas
        </h6>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'backup' ? 'active' : ''; ?>" 
                   href="backup.php">
                    <i class="fas fa-database me-2"></i>
                    Backup Data
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $current_page == 'analytics' ? 'active' : ''; ?>" 
                   href="analytics.php">
                    <i class="fas fa-chart-bar me-2"></i>
                    Statistik
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="../index.php" target="_blank">
                    <i class="fas fa-external-link-alt me-2"></i>
                    Lihat Website
                </a>
            </li>
        </ul>
        
        <!-- Quick Actions -->
        <h6 class="sidebar-heading">
            Aksi Cepat
        </h6>
        <ul class="nav flex-column mb-2">
            <li class="nav-item">
                <a class="nav-link text-success" href="products.php?action=add">
                    <i class="fas fa-plus me-2"></i>
                    Tambah Produk
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-info" href="gallery.php?action=add">
                    <i class="fas fa-upload me-2"></i>
                    Upload Foto
                </a>
            </li>
        </ul>
        
        <!-- Footer Info -->
        <div class="sidebar-footer mt-auto p-3">
            <div class="text-center">
                <small class="text-muted">
                    <i class="fas fa-tint me-1"></i>
                    Admin Panel v1.0
                </small>
            </div>
            <div class="text-center mt-2">
                <small class="text-muted">
                    Login: <?php echo $_SESSION['admin_username']; ?>
                </small>
            </div>
        </div>
    </div>
</nav>

<style>
/* Sidebar enhancements */
.sidebar .nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar .nav-link .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

.sidebar-heading {
    position: relative;
}

.sidebar-heading::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 1rem;
    right: 1rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, #dee2e6, transparent);
}

.sidebar-footer {
    border-top: 1px solid #dee2e6;
    background: rgba(0, 0, 0, 0.02);
}

/* Mobile sidebar */
@media (max-width: 767.98px) {
    .sidebar {
        background: white;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }
    
    .sidebar-sticky {
        height: 100vh;
        padding-top: 1rem;
    }
}

/* Hover effects */
.nav-link:hover .badge {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* Active state enhancements */
.nav-link.active {
    position: relative;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-color);
    border-radius: 0 3px 3px 0;
}
</style>
