-- Database setup for Agen Air Fazza
-- Run this SQL script in your MySQL/MariaDB client

-- Create database
CREATE DATABASE IF NOT EXISTS sim_airfazza CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE sim_airfazza;

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    image VARCHAR(255),
    category ENUM('air_isi_ulang', 'air_kemasan') NOT NULL,
    is_featured BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Gallery table
CREATE TABLE IF NOT EXISTS gallery (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image VARCHAR(255) NOT NULL,
    category ENUM('kegiatan', 'produk', 'fasilitas') NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Testimonials table
CREATE TABLE IF NOT EXISTS testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_photo VARCHAR(255),
    rating INT DEFAULT 5,
    message TEXT NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE IF NOT EXISTS contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status ENUM('unread', 'read', 'replied') DEFAULT 'unread',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Company info table
CREATE TABLE IF NOT EXISTS company_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    company_address TEXT NOT NULL,
    company_phone VARCHAR(50) NOT NULL,
    company_whatsapp VARCHAR(50) NOT NULL,
    company_email VARCHAR(255) NOT NULL,
    business_hours VARCHAR(255) NOT NULL,
    company_history TEXT,
    company_vision TEXT,
    company_mission TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default company info
INSERT INTO company_info (
    company_name, 
    company_address, 
    company_phone, 
    company_whatsapp, 
    company_email, 
    business_hours, 
    company_vision, 
    company_mission, 
    company_history
) VALUES (
    'Agen Air Fazza',
    'Jl. Contoh Alamat No. 123, Kota Anda, Provinsi, 12345',
    '+62 812-3456-7890',
    '+62 812-3456-7890',
    '<EMAIL>',
    'Senin - Sabtu: 08:00 - 17:00 WIB',
    'Menjadi penyedia air bersih terpercaya dan berkualitas tinggi untuk kesehatan masyarakat.',
    'Menyediakan air isi ulang dan air mineral kemasan dengan standar kualitas terbaik, pelayanan prima, dan harga terjangkau.',
    'Agen Air Fazza didirikan dengan komitmen untuk menyediakan air bersih berkualitas tinggi kepada masyarakat. Dengan pengalaman bertahun-tahun, kami terus berinovasi dalam memberikan pelayanan terbaik.'
) ON DUPLICATE KEY UPDATE id=id;

-- Insert default admin user (username: admin, password: admin123)
INSERT INTO admin_users (username, password, email, full_name) 
VALUES ('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Administrator')
ON DUPLICATE KEY UPDATE id=id;

-- Insert sample products
INSERT INTO products (name, description, price, category, is_featured, status) VALUES
('Air Isi Ulang 19 Liter', 'Air isi ulang berkualitas tinggi dengan proses penyaringan 7 tahap untuk kemurnian optimal.', 5000.00, 'air_isi_ulang', TRUE, 'active'),
('Air Isi Ulang 5 Liter', 'Air isi ulang dalam kemasan 5 liter, praktis untuk kebutuhan sehari-hari.', 2000.00, 'air_isi_ulang', FALSE, 'active'),
('Air Mineral Kemasan 600ml', 'Air mineral alami dalam kemasan botol 600ml, segar dan menyehatkan.', 3000.00, 'air_kemasan', TRUE, 'active'),
('Air Mineral Kemasan 1.5L', 'Air mineral premium dalam kemasan 1.5 liter untuk kebutuhan keluarga.', 5000.00, 'air_kemasan', FALSE, 'active'),
('Paket Air Isi Ulang Bulanan', 'Paket berlangganan air isi ulang untuk 1 bulan dengan harga hemat.', 120000.00, 'air_isi_ulang', TRUE, 'active')
ON DUPLICATE KEY UPDATE id=id;

-- Insert sample testimonials
INSERT INTO testimonials (customer_name, rating, message, status) VALUES
('Budi Santoso', 5, 'Air dari Agen Air Fazza sangat bersih dan segar. Pelayanannya juga ramah dan cepat. Sangat puas!', 'active'),
('Siti Nurhaliza', 5, 'Sudah berlangganan air isi ulang di sini selama 2 tahun. Kualitasnya konsisten dan harga terjangkau.', 'active'),
('Ahmad Rahman', 4, 'Pelayanan bagus, air berkualitas. Pengiriman selalu tepat waktu. Recommended!', 'active'),
('Maya Sari', 5, 'Air mineral kemasannya enak dan segar. Cocok untuk keluarga. Terima kasih Agen Air Fazza!', 'active')
ON DUPLICATE KEY UPDATE id=id;

-- Insert sample gallery items
INSERT INTO gallery (title, description, image, category, status) VALUES
('Fasilitas Produksi Modern', 'Fasilitas produksi dengan teknologi penyaringan terdepan', 'facility-1.jpg', 'fasilitas', 'active'),
('Proses Penyaringan 7 Tahap', 'Proses penyaringan air melalui 7 tahap untuk kemurnian optimal', 'process-1.jpg', 'kegiatan', 'active'),
('Produk Air Isi Ulang', 'Berbagai ukuran kemasan air isi ulang berkualitas tinggi', 'product-1.jpg', 'produk', 'active'),
('Tim Pengiriman Profesional', 'Tim pengiriman yang berpengalaman dan terpercaya', 'delivery-1.jpg', 'kegiatan', 'active')
ON DUPLICATE KEY UPDATE id=id;

-- Show success message
SELECT 'Database setup completed successfully!' as message;
