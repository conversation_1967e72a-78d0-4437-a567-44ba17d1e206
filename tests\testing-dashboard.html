<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testing Dashboard - Agen Air Fazza</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #2563eb, #06b6d4);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .test-card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }
        
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .test-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        .status-pending { background-color: #6c757d; }
        .status-running { background-color: #ffc107; animation: pulse 1s infinite; }
        .status-passed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .status-error { background-color: #fd7e14; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
        }
        
        .progress-ring-circle {
            stroke: #e9ecef;
            stroke-width: 8;
            fill: transparent;
            r: 52;
            cx: 60;
            cy: 60;
        }
        
        .progress-ring-progress {
            stroke: #28a745;
            stroke-width: 8;
            stroke-linecap: round;
            fill: transparent;
            r: 52;
            cx: 60;
            cy: 60;
            stroke-dasharray: 326.73;
            stroke-dashoffset: 326.73;
            transition: stroke-dashoffset 0.5s ease;
        }
        
        .test-log {
            background: #1e1e1e;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 400px;
            overflow-y: auto;
            border-radius: 0.5rem;
            padding: 1rem;
        }
        
        .log-entry {
            margin-bottom: 0.25rem;
            padding: 0.25rem 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        
        .metric-card {
            text-align: center;
            padding: 1.5rem;
            border-radius: 0.75rem;
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .test-category {
            border-left: 4px solid #2563eb;
            padding-left: 1rem;
            margin-bottom: 1rem;
        }
        
        .btn-test {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
            color: white;
        }
        
        .btn-test:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-vial me-3"></i>
                        Testing Dashboard
                    </h1>
                    <p class="mb-0 mt-2 opacity-75">Comprehensive Quality Assurance for Agen Air Fazza</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <button id="runAllTests" class="btn btn-test">
                        <i class="fas fa-play me-2"></i>
                        Run All Tests
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Test Overview -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-primary" id="totalTests">0</div>
                    <div class="metric-label">Total Tests</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-success" id="passedTests">0</div>
                    <div class="metric-label">Passed</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-danger" id="failedTests">0</div>
                    <div class="metric-label">Failed</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-info" id="successRate">0%</div>
                    <div class="metric-label">Success Rate</div>
                </div>
            </div>
        </div>

        <!-- Progress Circle -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <div class="card test-card">
                    <div class="card-body">
                        <h5 class="card-title">Test Progress</h5>
                        <svg class="progress-ring mx-auto">
                            <circle class="progress-ring-circle"></circle>
                            <circle class="progress-ring-progress" id="progressCircle"></circle>
                        </svg>
                        <div class="mt-3">
                            <div id="progressText" class="h4">Ready to Start</div>
                            <div id="progressSubtext" class="text-muted">Click "Run All Tests" to begin</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Categories -->
        <div class="row">
            <div class="col-md-8">
                <div class="card test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list-check me-2"></i>
                            Test Results
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-flask fa-3x mb-3"></i>
                                <p>No tests have been run yet. Click "Run All Tests" to start testing.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card test-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-terminal me-2"></i>
                            Test Log
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div id="testLog" class="test-log">
                            <div class="log-entry log-info">Testing dashboard initialized</div>
                            <div class="log-entry log-info">Ready to run tests...</div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card test-card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="testSpecificPage('home')">
                                <i class="fas fa-home me-2"></i>
                                Test Homepage
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="testSpecificPage('products')">
                                <i class="fas fa-box me-2"></i>
                                Test Products
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="testSpecificPage('contact')">
                                <i class="fas fa-envelope me-2"></i>
                                Test Contact
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="clearResults()">
                                <i class="fas fa-trash me-2"></i>
                                Clear Results
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="exportResults()">
                                <i class="fas fa-download me-2"></i>
                                Export Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Details Modal -->
        <div class="modal fade" id="testDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Test Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div id="testDetailsContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="automated-tests.js"></script>
    
    <script>
        class TestingDashboard {
            constructor() {
                this.testSuite = null;
                this.isRunning = false;
                this.results = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadTestSuite();
            }

            setupEventListeners() {
                document.getElementById('runAllTests').addEventListener('click', () => {
                    this.runAllTests();
                });
            }

            loadTestSuite() {
                try {
                    this.testSuite = initializeTestSuite();
                    this.updateMetrics();
                    this.log('Test suite loaded successfully', 'success');
                } catch (error) {
                    this.log(`Failed to load test suite: ${error.message}`, 'error');
                }
            }

            async runAllTests() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.updateRunButton(true);
                this.clearResults();
                this.log('Starting test execution...', 'info');
                
                try {
                    this.results = await this.testSuite.runAllTests();
                    this.displayResults();
                    this.updateMetrics();
                    this.updateProgress(100);
                    this.log('All tests completed', 'success');
                } catch (error) {
                    this.log(`Test execution failed: ${error.message}`, 'error');
                } finally {
                    this.isRunning = false;
                    this.updateRunButton(false);
                }
            }

            displayResults() {
                const resultsContainer = document.getElementById('testResults');
                resultsContainer.innerHTML = '';

                // Group results by category
                const categories = {};
                this.results.details.forEach(test => {
                    if (!categories[test.category]) {
                        categories[test.category] = [];
                    }
                    categories[test.category].push(test);
                });

                // Display each category
                Object.keys(categories).forEach(categoryName => {
                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'test-category';
                    
                    const categoryTests = categories[categoryName];
                    const passed = categoryTests.filter(t => t.status === 'passed').length;
                    const total = categoryTests.length;
                    
                    categoryDiv.innerHTML = `
                        <h6 class="mb-3">${categoryName} (${passed}/${total})</h6>
                        ${categoryTests.map(test => this.createTestResultHTML(test)).join('')}
                    `;
                    
                    resultsContainer.appendChild(categoryDiv);
                });
            }

            createTestResultHTML(test) {
                const statusClass = `status-${test.status}`;
                const iconClass = test.status === 'passed' ? 'fa-check-circle text-success' : 
                                 test.status === 'failed' ? 'fa-times-circle text-danger' : 
                                 'fa-exclamation-triangle text-warning';
                
                return `
                    <div class="d-flex align-items-center justify-content-between py-2 border-bottom">
                        <div class="d-flex align-items-center">
                            <span class="test-status ${statusClass}"></span>
                            <span class="me-2">${test.name}</span>
                            <i class="fas ${iconClass}"></i>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">${test.duration}ms</small>
                            ${test.status !== 'passed' ? 
                                `<button class="btn btn-sm btn-outline-info ms-2" onclick="dashboard.showTestDetails('${test.name}')">
                                    <i class="fas fa-info-circle"></i>
                                </button>` : ''
                            }
                        </div>
                    </div>
                `;
            }

            updateMetrics() {
                if (!this.results) {
                    document.getElementById('totalTests').textContent = this.testSuite ? this.testSuite.tests.length : 0;
                    return;
                }

                document.getElementById('totalTests').textContent = this.results.total;
                document.getElementById('passedTests').textContent = this.results.passed;
                document.getElementById('failedTests').textContent = this.results.failed;
                
                const successRate = this.results.total > 0 ? 
                    ((this.results.passed / this.results.total) * 100).toFixed(1) : 0;
                document.getElementById('successRate').textContent = `${successRate}%`;
            }

            updateProgress(percentage) {
                const circle = document.getElementById('progressCircle');
                const circumference = 2 * Math.PI * 52;
                const offset = circumference - (percentage / 100) * circumference;
                
                circle.style.strokeDashoffset = offset;
                
                if (percentage === 100) {
                    const successRate = this.results ? 
                        ((this.results.passed / this.results.total) * 100).toFixed(1) : 0;
                    document.getElementById('progressText').textContent = `${successRate}% Success`;
                    document.getElementById('progressSubtext').textContent = 'Testing completed';
                    
                    // Change color based on success rate
                    if (successRate >= 90) {
                        circle.style.stroke = '#28a745';
                    } else if (successRate >= 70) {
                        circle.style.stroke = '#ffc107';
                    } else {
                        circle.style.stroke = '#dc3545';
                    }
                }
            }

            updateRunButton(isRunning) {
                const button = document.getElementById('runAllTests');
                if (isRunning) {
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Running Tests...';
                    button.disabled = true;
                } else {
                    button.innerHTML = '<i class="fas fa-play me-2"></i>Run All Tests';
                    button.disabled = false;
                }
            }

            log(message, type = 'info') {
                const logContainer = document.getElementById('testLog');
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry log-${type}`;
                
                const timestamp = new Date().toLocaleTimeString();
                logEntry.textContent = `[${timestamp}] ${message}`;
                
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
            }

            showTestDetails(testName) {
                const test = this.results.details.find(t => t.name === testName);
                if (!test) return;

                const modal = new bootstrap.Modal(document.getElementById('testDetailsModal'));
                const content = document.getElementById('testDetailsContent');
                
                content.innerHTML = `
                    <h6>Test: ${test.name}</h6>
                    <p><strong>Category:</strong> ${test.category}</p>
                    <p><strong>Status:</strong> <span class="badge bg-${test.status === 'passed' ? 'success' : 'danger'}">${test.status}</span></p>
                    <p><strong>Duration:</strong> ${test.duration}ms</p>
                    <p><strong>Message:</strong></p>
                    <div class="alert alert-${test.status === 'passed' ? 'success' : 'danger'}">
                        ${test.message}
                    </div>
                `;
                
                modal.show();
            }

            clearResults() {
                document.getElementById('testResults').innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-flask fa-3x mb-3"></i>
                        <p>Running tests...</p>
                    </div>
                `;
                
                this.updateProgress(0);
                document.getElementById('progressText').textContent = 'Running Tests';
                document.getElementById('progressSubtext').textContent = 'Please wait...';
            }

            exportResults() {
                if (!this.results) {
                    alert('No test results to export. Please run tests first.');
                    return;
                }

                const report = {
                    timestamp: new Date().toISOString(),
                    summary: {
                        total: this.results.total,
                        passed: this.results.passed,
                        failed: this.results.failed,
                        successRate: ((this.results.passed / this.results.total) * 100).toFixed(1)
                    },
                    details: this.results.details
                };

                const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `test-report-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);
            }
        }

        // Global functions
        function testSpecificPage(page) {
            window.open(`../?page=${page}`, '_blank');
        }

        function clearResults() {
            dashboard.clearResults();
        }

        function exportResults() {
            dashboard.exportResults();
        }

        // Initialize dashboard
        const dashboard = new TestingDashboard();
    </script>
</body>
</html>
