<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get and sanitize input data
$name = isset($_POST['name']) ? sanitizeInput($_POST['name']) : '';
$email = isset($_POST['email']) ? sanitizeInput($_POST['email']) : '';
$message = isset($_POST['message']) ? sanitizeInput($_POST['message']) : '';

// Validation
$errors = [];

if (empty($name)) {
    $errors[] = 'Nama lengkap harus diisi';
} elseif (strlen($name) < 2) {
    $errors[] = '<PERSON>a lengkap minimal 2 karakter';
}

if (empty($email)) {
    $errors[] = 'Email harus diisi';
} elseif (!validateEmail($email)) {
    $errors[] = 'Format email tidak valid';
}

if (empty($message)) {
    $errors[] = 'Pesan harus diisi';
} elseif (strlen($message) < 10) {
    $errors[] = 'Pesan minimal 10 karakter';
}

// If there are validation errors
if (!empty($errors)) {
    echo json_encode([
        'success' => false,
        'message' => implode(', ', $errors)
    ]);
    exit;
}

// Try to send the message
$result = sendContactMessage($name, $email, $message);

// Return JSON response
echo json_encode($result);
?>
