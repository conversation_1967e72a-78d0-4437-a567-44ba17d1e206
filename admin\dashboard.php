<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isAdmin()) {
    header('Location: index.php');
    exit();
}

// Get dashboard statistics
try {
    // Count products
    $stmt = $pdo->query("SELECT COUNT(*) as total, 
                         SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                         SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured
                         FROM products");
    $product_stats = $stmt->fetch();
    
    // Count testimonials
    $stmt = $pdo->query("SELECT COUNT(*) as total,
                         SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active
                         FROM testimonials");
    $testimonial_stats = $stmt->fetch();
    
    // Count gallery images
    $stmt = $pdo->query("SELECT COUNT(*) as total,
                         SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active
                         FROM gallery");
    $gallery_stats = $stmt->fetch();
    
    // Count contact messages
    $stmt = $pdo->query("SELECT COUNT(*) as total,
                         SUM(CASE WHEN status = 'unread' THEN 1 ELSE 0 END) as unread,
                         SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as read
                         FROM contact_messages");
    $message_stats = $stmt->fetch();
    
    // Get recent contact messages
    $stmt = $pdo->query("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 5");
    $recent_messages = $stmt->fetchAll();
    
    // Get recent products
    $stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' ORDER BY created_at DESC LIMIT 5");
    $recent_products = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "Error loading dashboard data: " . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download me-1"></i>
                            Export
                        </button>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        Tambah Konten
                    </button>
                </div>
            </div>
            
            <!-- Welcome Message -->
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                Selamat datang, <strong><?php echo $_SESSION['admin_name']; ?></strong>! 
                Anda login sebagai administrator pada <?php echo date('d F Y, H:i'); ?> WIB.
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Produk
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $product_stats['total']; ?>
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo $product_stats['active']; ?> aktif, 
                                        <?php echo $product_stats['featured']; ?> unggulan
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-box fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Testimoni
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $testimonial_stats['total']; ?>
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo $testimonial_stats['active']; ?> aktif
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-comments fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Galeri Foto
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $gallery_stats['total']; ?>
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo $gallery_stats['active']; ?> aktif
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-images fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Pesan Kontak
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $message_stats['total']; ?>
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo $message_stats['unread']; ?> belum dibaca
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-envelope fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Content Row -->
            <div class="row">
                <!-- Recent Messages -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-envelope me-2"></i>
                                Pesan Terbaru
                            </h6>
                            <a href="messages.php" class="btn btn-sm btn-primary">
                                Lihat Semua
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recent_messages)): ?>
                                <?php foreach ($recent_messages as $message): ?>
                                    <div class="message-item mb-3 pb-3 border-bottom">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php echo htmlspecialchars($message['name']); ?>
                                                    <?php if ($message['status'] == 'unread'): ?>
                                                        <span class="badge bg-warning">Baru</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <p class="text-muted small mb-1"><?php echo htmlspecialchars($message['email']); ?></p>
                                                <p class="mb-1"><?php echo substr(htmlspecialchars($message['message']), 0, 100) . '...'; ?></p>
                                            </div>
                                            <small class="text-muted"><?php echo formatDate($message['created_at']); ?></small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted text-center">Belum ada pesan masuk.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Products -->
                <div class="col-lg-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-box me-2"></i>
                                Produk Terbaru
                            </h6>
                            <a href="products.php" class="btn btn-sm btn-primary">
                                Kelola Produk
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recent_products)): ?>
                                <?php foreach ($recent_products as $product): ?>
                                    <div class="product-item mb-3 pb-3 border-bottom">
                                        <div class="d-flex align-items-center">
                                            <div class="product-image me-3">
                                                <img src="../assets/images/products/<?php echo $product['image']; ?>" 
                                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                     class="rounded" style="width: 50px; height: 50px; object-fit: cover;">
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1">
                                                    <?php echo htmlspecialchars($product['name']); ?>
                                                    <?php if ($product['is_featured']): ?>
                                                        <span class="badge bg-success">Unggulan</span>
                                                    <?php endif; ?>
                                                </h6>
                                                <p class="text-muted small mb-0">
                                                    <?php echo formatCurrency($product['price']); ?> - 
                                                    <?php echo ucfirst(str_replace('_', ' ', $product['category'])); ?>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="text-muted text-center">Belum ada produk.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-bolt me-2"></i>
                                Aksi Cepat
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="products.php?action=add" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-plus-circle mb-2 d-block fs-4"></i>
                                        Tambah Produk
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="gallery.php?action=add" class="btn btn-outline-success w-100">
                                        <i class="fas fa-image mb-2 d-block fs-4"></i>
                                        Upload Foto
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="testimonials.php?action=add" class="btn btn-outline-info w-100">
                                        <i class="fas fa-comment-plus mb-2 d-block fs-4"></i>
                                        Tambah Testimoni
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="settings.php" class="btn btn-outline-warning w-100">
                                        <i class="fas fa-cog mb-2 d-block fs-4"></i>
                                        Pengaturan
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
