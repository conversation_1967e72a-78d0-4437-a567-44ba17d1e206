<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isAdmin()) {
    header('Location: index.php');
    exit();
}

$page_title = 'Info Perusahaan';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $company_name = sanitizeInput($_POST['company_name']);
    $company_address = sanitizeInput($_POST['company_address']);
    $company_phone = sanitizeInput($_POST['company_phone']);
    $company_whatsapp = sanitizeInput($_POST['company_whatsapp']);
    $company_email = sanitizeInput($_POST['company_email']);
    $business_hours = sanitizeInput($_POST['business_hours']);
    $company_history = sanitizeInput($_POST['company_history']);
    $company_vision = sanitizeInput($_POST['company_vision']);
    $company_mission = sanitizeInput($_POST['company_mission']);
    
    try {
        // Check if company info exists
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM company_info");
        $exists = $stmt->fetch()['count'] > 0;
        
        if ($exists) {
            // Update existing record
            $stmt = $pdo->prepare("UPDATE company_info SET 
                company_name = ?, company_address = ?, company_phone = ?, 
                company_whatsapp = ?, company_email = ?, business_hours = ?,
                company_history = ?, company_vision = ?, company_mission = ?,
                updated_at = NOW()");
            $stmt->execute([
                $company_name, $company_address, $company_phone,
                $company_whatsapp, $company_email, $business_hours,
                $company_history, $company_vision, $company_mission
            ]);
        } else {
            // Insert new record
            $stmt = $pdo->prepare("INSERT INTO company_info 
                (company_name, company_address, company_phone, company_whatsapp, 
                 company_email, business_hours, company_history, company_vision, 
                 company_mission, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([
                $company_name, $company_address, $company_phone,
                $company_whatsapp, $company_email, $business_hours,
                $company_history, $company_vision, $company_mission
            ]);
        }
        
        $success_message = 'Informasi perusahaan berhasil disimpan.';
    } catch (Exception $e) {
        $error_message = 'Gagal menyimpan informasi perusahaan: ' . $e->getMessage();
    }
}

// Get current company info
try {
    $stmt = $pdo->query("SELECT * FROM company_info LIMIT 1");
    $company_info = $stmt->fetch();
} catch (Exception $e) {
    $error_message = 'Gagal memuat informasi perusahaan: ' . $e->getMessage();
    $company_info = null;
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-building me-2"></i>
                    Info Perusahaan
                </h1>
            </div>
            
            <!-- Alert Messages -->
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Company Info Form -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informasi Perusahaan</h6>
                </div>
                <div class="card-body">
                    <form method="POST" data-autosave="company_form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company_name" class="form-label">Nama Perusahaan *</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="<?php echo $company_info ? htmlspecialchars($company_info['company_name']) : ''; ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_address" class="form-label">Alamat Lengkap *</label>
                                    <textarea class="form-control" id="company_address" name="company_address" rows="3" 
                                              data-autoresize required><?php echo $company_info ? htmlspecialchars($company_info['company_address']) : ''; ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_phone" class="form-label">Nomor Telepon *</label>
                                    <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                           value="<?php echo $company_info ? htmlspecialchars($company_info['company_phone']) : ''; ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_whatsapp" class="form-label">WhatsApp *</label>
                                    <input type="tel" class="form-control" id="company_whatsapp" name="company_whatsapp" 
                                           value="<?php echo $company_info ? htmlspecialchars($company_info['company_whatsapp']) : ''; ?>" required>
                                    <div class="form-text">Format: +62812345678 atau 0812345678</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company_email" class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="company_email" name="company_email" 
                                           value="<?php echo $company_info ? htmlspecialchars($company_info['company_email']) : ''; ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="business_hours" class="form-label">Jam Operasional *</label>
                                    <input type="text" class="form-control" id="business_hours" name="business_hours" 
                                           value="<?php echo $company_info ? htmlspecialchars($company_info['business_hours']) : ''; ?>" 
                                           placeholder="Senin - Sabtu: 08:00 - 17:00" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_history" class="form-label">Sejarah Perusahaan</label>
                                    <textarea class="form-control" id="company_history" name="company_history" rows="4" 
                                              data-autoresize><?php echo $company_info ? htmlspecialchars($company_info['company_history']) : ''; ?></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company_vision" class="form-label">Visi Perusahaan</label>
                                    <textarea class="form-control" id="company_vision" name="company_vision" rows="3" 
                                              data-autoresize><?php echo $company_info ? htmlspecialchars($company_info['company_vision']) : ''; ?></textarea>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company_mission" class="form-label">Misi Perusahaan</label>
                                    <textarea class="form-control" id="company_mission" name="company_mission" rows="3" 
                                              data-autoresize><?php echo $company_info ? htmlspecialchars($company_info['company_mission']) : ''; ?></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border-top pt-3 mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                Simpan Informasi
                            </button>
                            <button type="button" class="btn btn-secondary ms-2" onclick="previewWebsite()">
                                <i class="fas fa-eye me-1"></i>
                                Preview Website
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Quick Stats -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Status Website
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo $company_info ? 'Aktif' : 'Belum Dikonfigurasi'; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-globe fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Kontak Tersedia
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        $contact_count = 0;
                                        if ($company_info) {
                                            if ($company_info['company_phone']) $contact_count++;
                                            if ($company_info['company_whatsapp']) $contact_count++;
                                            if ($company_info['company_email']) $contact_count++;
                                        }
                                        echo $contact_count . '/3';
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-phone fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Terakhir Diperbarui
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php 
                                        if ($company_info && $company_info['updated_at']) {
                                            echo date('d/m/Y', strtotime($company_info['updated_at']));
                                        } else {
                                            echo 'Belum pernah';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function previewWebsite() {
    window.open('../index.php', '_blank');
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[data-autosave]');
    
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            showAlert('Mohon lengkapi semua field yang wajib diisi.', 'danger');
        }
    });
    
    // Real-time validation
    const inputs = form.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
