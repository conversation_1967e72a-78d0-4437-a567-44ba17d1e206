<?php
$company_info = getCompanyInfo();
?>

<footer class="footer bg-dark text-light">
    <div class="container">
        <div class="row py-5">
            <!-- Company Info -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="footer-brand mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="footer-logo me-3">
                            <i class="fas fa-tint text-primary fs-2"></i>
                        </div>
                        <div>
                            <h5 class="text-white mb-1"><?php echo $company_info['company_name']; ?></h5>
                            <p class="text-muted small mb-0">Air Bersih, Hidup Sehat</p>
                        </div>
                    </div>
                </div>
                
                <p class="text-muted mb-3">
                    Kami berkomitmen menyediakan air bersih berkualitas tinggi untuk kesehatan keluarga Anda. 
                    Dengan pengalaman bertahun-tahun, kepercayaan Anda adalah prioritas utama kami.
                </p>
                
                <div class="social-links">
                    <h6 class="text-primary mb-3">I<PERSON><PERSON></h6>
                    <div class="d-flex gap-3 mb-3">
                        <a href="#" class="social-link" title="Facebook" data-bs-toggle="tooltip">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-link" title="Instagram" data-bs-toggle="tooltip">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-link" title="YouTube" data-bs-toggle="tooltip">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>"
                           class="social-link" title="WhatsApp" target="_blank" data-bs-toggle="tooltip">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                        <a href="mailto:<?php echo $company_info['company_email']; ?>"
                           class="social-link" title="Email" data-bs-toggle="tooltip">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>

                    <!-- Newsletter Signup -->
                    <div class="newsletter-signup">
                        <h6 class="text-primary mb-2">Newsletter</h6>
                        <p class="text-muted small mb-3">Dapatkan info terbaru dan penawaran khusus</p>
                        <form class="newsletter-form" id="newsletterForm">
                            <div class="input-group input-group-sm">
                                <input type="email" class="form-control" placeholder="Email Anda" required>
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="text-primary mb-3">Menu Utama</h6>
                <ul class="list-unstyled footer-links">
                    <li><a href="index.php?page=home">Beranda</a></li>
                    <li><a href="index.php?page=products">Produk</a></li>
                    <li><a href="index.php?page=profile">Profil Usaha</a></li>
                    <li><a href="index.php?page=gallery">Galeri & Testimoni</a></li>
                    <li><a href="index.php?page=contact">Kontak Kami</a></li>
                </ul>

                <h6 class="text-primary mb-3 mt-4">Layanan</h6>
                <ul class="list-unstyled footer-links">
                    <li><a href="index.php?page=products&category=air_isi_ulang">Air Isi Ulang</a></li>
                    <li><a href="index.php?page=products&category=air_kemasan">Air Kemasan</a></li>
                    <li><a href="#" onclick="openWhatsApp('Saya ingin info tentang layanan antar')">Layanan Antar</a></li>
                    <li><a href="#" onclick="openWhatsApp('Saya ingin info tentang berlangganan')">Berlangganan</a></li>
                </ul>
            </div>

            <!-- Products & Certifications -->
            <div class="col-lg-3 col-md-6 mb-4">
                <h6 class="text-primary mb-3">Produk Kami</h6>
                <ul class="list-unstyled footer-links">
                    <li><a href="index.php?page=products&category=air_isi_ulang">Air Isi Ulang Premium</a></li>
                    <li><a href="index.php?page=products&category=air_isi_ulang">Air Isi Ulang Reguler</a></li>
                    <li><a href="index.php?page=products&category=air_kemasan">Air Mineral Kemasan</a></li>
                    <li><a href="index.php?page=products">Paket Berlangganan</a></li>
                </ul>

                <div class="mt-4">
                    <h6 class="text-primary mb-3">Sertifikasi</h6>
                    <div class="certifications">
                        <div class="cert-item mb-2">
                            <i class="fas fa-certificate text-warning me-2"></i>
                            <span class="text-muted small">Standar SNI</span>
                        </div>
                        <div class="cert-item mb-2">
                            <i class="fas fa-shield-alt text-success me-2"></i>
                            <span class="text-muted small">BPOM Certified</span>
                        </div>
                        <div class="cert-item mb-2">
                            <i class="fas fa-leaf text-success me-2"></i>
                            <span class="text-muted small">Eco Friendly</span>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6 class="text-primary mb-3">Jam Operasional</h6>
                    <p class="text-muted small mb-2">
                        <i class="fas fa-clock me-2"></i>
                        <?php echo $company_info['business_hours']; ?>
                    </p>
                    <div class="business-status">
                        <span class="status-indicator" id="businessStatus">
                            <i class="fas fa-circle me-1"></i>
                            <span id="statusText">Memuat...</span>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Contact Info -->
            <div class="col-lg-3 col-md-6 mb-4">
                <h6 class="text-primary mb-3">Hubungi Kami</h6>
                
                <div class="contact-info">
                    <div class="contact-item mb-3">
                        <i class="fas fa-map-marker-alt text-primary me-3"></i>
                        <div>
                            <strong class="d-block text-white">Alamat</strong>
                            <span class="text-muted small"><?php echo $company_info['company_address']; ?></span>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <i class="fas fa-phone text-primary me-3"></i>
                        <div>
                            <strong class="d-block text-white">Telepon</strong>
                            <a href="tel:<?php echo $company_info['company_phone']; ?>" 
                               class="text-muted small"><?php echo $company_info['company_phone']; ?></a>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <i class="fab fa-whatsapp text-primary me-3"></i>
                        <div>
                            <strong class="d-block text-white">WhatsApp</strong>
                            <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>" 
                               class="text-muted small" target="_blank"><?php echo $company_info['company_whatsapp']; ?></a>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <i class="fas fa-envelope text-primary me-3"></i>
                        <div>
                            <strong class="d-block text-white">Email</strong>
                            <a href="mailto:<?php echo $company_info['company_email']; ?>" 
                               class="text-muted small"><?php echo $company_info['company_email']; ?></a>
                        </div>
                    </div>
                </div>

                <!-- CTA Button -->
                <div class="mt-4">
                    <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>?text=Halo, saya ingin bertanya tentang produk air Agen Air Fazza" 
                       class="btn btn-primary btn-sm w-100" target="_blank">
                        <i class="fab fa-whatsapp me-2"></i>
                        Pesan Sekarang
                    </a>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="border-top border-secondary pt-4 pb-3">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="text-muted small mb-2">
                                &copy; <?php echo date('Y'); ?> <?php echo $company_info['company_name']; ?>.
                                Semua hak cipta dilindungi.
                            </p>
                            <p class="text-muted small mb-0">
                                <i class="fas fa-code me-1"></i>
                                Dibuat dengan <i class="fas fa-heart text-danger mx-1"></i> untuk pelanggan terbaik
                            </p>
                        </div>
                        <div class="col-md-6">
                            <div class="payment-methods mb-2">
                                <span class="text-muted small d-block mb-2">Metode Pembayaran:</span>
                                <div class="payment-icons">
                                    <i class="fas fa-money-bill-wave text-success me-2" title="Tunai"></i>
                                    <i class="fas fa-university text-primary me-2" title="Transfer Bank"></i>
                                    <i class="fas fa-mobile-alt text-info me-2" title="E-Wallet"></i>
                                    <i class="fas fa-qrcode text-warning" title="QRIS"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="footer-links-bottom">
                        <a href="#" class="text-muted small me-3" data-bs-toggle="modal" data-bs-target="#privacyModal">Kebijakan Privasi</a>
                        <a href="#" class="text-muted small me-3" data-bs-toggle="modal" data-bs-target="#termsModal">Syarat & Ketentuan</a>
                        <a href="#" class="text-muted small me-3" onclick="showSitemap()">Sitemap</a>
                        <a href="admin/" class="text-muted small">Admin</a>
                    </div>
                    <div class="website-info mt-2">
                        <span class="text-muted small">
                            <i class="fas fa-globe me-1"></i>
                            Version 1.0 |
                            <span id="lastUpdated">Updated <?php echo date('M Y'); ?></span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Floating Action Buttons -->
<div class="floating-actions">
    <!-- Back to Top Button -->
    <div class="back-to-top" title="Kembali ke Atas" data-bs-toggle="tooltip">
        <i class="fas fa-arrow-up"></i>
    </div>

    <!-- WhatsApp Floating Button -->
    <div class="whatsapp-float" title="Chat WhatsApp" data-bs-toggle="tooltip">
        <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>?text=Halo, saya ingin bertanya tentang produk Agen Air Fazza"
           target="_blank">
            <i class="fab fa-whatsapp"></i>
        </a>
    </div>

    <!-- Phone Floating Button -->
    <div class="phone-float" title="Telepon Sekarang" data-bs-toggle="tooltip">
        <a href="tel:<?php echo $company_info['company_phone']; ?>">
            <i class="fas fa-phone"></i>
        </a>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">Memuat...</p>
    </div>
</div>

<!-- Cookie Consent Banner -->
<div class="cookie-banner" id="cookieBanner">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <p class="mb-0">
                    <i class="fas fa-cookie-bite me-2"></i>
                    Website ini menggunakan cookies untuk meningkatkan pengalaman Anda.
                    <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Pelajari lebih lanjut</a>
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <button class="btn btn-primary btn-sm me-2" onclick="acceptCookies()">Terima</button>
                <button class="btn btn-outline-secondary btn-sm" onclick="declineCookies()">Tolak</button>
            </div>
        </div>
    </div>
</div>

<!-- Quick Contact Modal -->
<div class="modal fade" id="quickContactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-phone me-2"></i>
                    Kontak Cepat
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-6">
                        <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>"
                           class="btn btn-success w-100" target="_blank">
                            <i class="fab fa-whatsapp mb-2 d-block fs-4"></i>
                            WhatsApp
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="tel:<?php echo $company_info['company_phone']; ?>"
                           class="btn btn-primary w-100">
                            <i class="fas fa-phone mb-2 d-block fs-4"></i>
                            Telepon
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="mailto:<?php echo $company_info['company_email']; ?>"
                           class="btn btn-info w-100">
                            <i class="fas fa-envelope mb-2 d-block fs-4"></i>
                            Email
                        </a>
                    </div>
                    <div class="col-6">
                        <a href="index.php?page=contact"
                           class="btn btn-outline-primary w-100">
                            <i class="fas fa-map-marker-alt mb-2 d-block fs-4"></i>
                            Lokasi
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Policy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-shield-alt me-2"></i>
                    Kebijakan Privasi
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Pengumpulan Informasi</h6>
                <p>Kami mengumpulkan informasi yang Anda berikan secara sukarela melalui formulir kontak, pemesanan, dan interaksi lainnya dengan website kami.</p>

                <h6>2. Penggunaan Informasi</h6>
                <p>Informasi yang dikumpulkan digunakan untuk:</p>
                <ul>
                    <li>Memproses pesanan dan memberikan layanan</li>
                    <li>Berkomunikasi dengan pelanggan</li>
                    <li>Meningkatkan kualitas layanan</li>
                    <li>Mengirim informasi produk dan promosi (dengan persetujuan)</li>
                </ul>

                <h6>3. Perlindungan Data</h6>
                <p>Kami berkomitmen melindungi informasi pribadi Anda dengan standar keamanan yang tinggi dan tidak akan membagikannya kepada pihak ketiga tanpa persetujuan.</p>

                <h6>4. Cookies</h6>
                <p>Website ini menggunakan cookies untuk meningkatkan pengalaman pengguna dan menganalisis traffic website.</p>

                <h6>5. Kontak</h6>
                <p>Untuk pertanyaan tentang kebijakan privasi, hubungi kami di <?php echo $company_info['company_email']; ?></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Terms & Conditions Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-contract me-2"></i>
                    Syarat & Ketentuan
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Penerimaan Syarat</h6>
                <p>Dengan menggunakan layanan kami, Anda menyetujui syarat dan ketentuan yang berlaku.</p>

                <h6>2. Layanan</h6>
                <p>Kami menyediakan layanan penjualan dan pengiriman air isi ulang dan air mineral kemasan dengan kualitas terjamin.</p>

                <h6>3. Pemesanan</h6>
                <ul>
                    <li>Pemesanan dapat dilakukan melalui WhatsApp, telepon, atau website</li>
                    <li>Konfirmasi pesanan akan diberikan dalam 1x24 jam</li>
                    <li>Pembatalan pesanan dapat dilakukan maksimal 2 jam sebelum pengiriman</li>
                </ul>

                <h6>4. Pembayaran</h6>
                <ul>
                    <li>Pembayaran dapat dilakukan tunai, transfer bank, atau e-wallet</li>
                    <li>Untuk pelanggan berlangganan, pembayaran dilakukan di awal periode</li>
                </ul>

                <h6>5. Pengiriman</h6>
                <ul>
                    <li>Pengiriman dilakukan sesuai jadwal yang disepakati</li>
                    <li>Keterlambatan di luar kendali kami akan dikomunikasikan</li>
                </ul>

                <h6>6. Garansi Kualitas</h6>
                <p>Kami menjamin kualitas produk sesuai standar SNI dan BPOM. Komplain dapat disampaikan dalam 24 jam setelah pengiriman.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Footer Styles */
.footer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
}

.footer-logo {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white !important;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: #d1d5db;
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    display: inline-block;
}

.footer-links a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.social-links .social-link {
    width: 40px;
    height: 40px;
    background: rgba(37, 99, 235, 0.1);
    border: 1px solid rgba(37, 99, 235, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links .social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
}

.contact-info .contact-item {
    display: flex;
    align-items: flex-start;
}

.contact-info .contact-item i {
    width: 20px;
    margin-top: 2px;
}

.contact-info a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-info a:hover {
    color: var(--primary-color);
}

/* Newsletter Signup */
.newsletter-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: none;
    color: white;
}

/* Certifications */
.cert-item {
    display: flex;
    align-items: center;
}

/* Business Status */
.business-status {
    font-size: 0.8rem;
}

.status-indicator.open {
    color: #10b981;
}

.status-indicator.closed {
    color: #ef4444;
}

/* Payment Methods */
.payment-icons i {
    font-size: 1.2rem;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.payment-icons i:hover {
    opacity: 1;
}

/* Floating Actions */
.floating-actions {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Back to Top Button */
.back-to-top {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
    text-decoration: none;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

/* WhatsApp Floating Button */
.whatsapp-float {
    width: 55px;
    height: 55px;
    background: linear-gradient(135deg, #25d366, #128c7e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    animation: pulse 2s infinite;
}

.whatsapp-float a {
    color: white;
    font-size: 1.5rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
}

/* Phone Floating Button */
.phone-float {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.phone-float a {
    color: white;
    font-size: 1.2rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.phone-float:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* Pulse Animation */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
}

/* Cookie Banner */
.cookie-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 1rem 0;
    z-index: 1001;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.cookie-banner.show {
    transform: translateY(0);
}

.cookie-banner a {
    color: var(--primary-color);
    text-decoration: underline;
}

.cookie-banner a:hover {
    color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .footer .row > div {
        text-align: center;
        margin-bottom: 2rem;
    }

    .contact-info .contact-item {
        justify-content: center;
        text-align: left;
    }

    .social-links {
        text-align: center;
    }

    .floating-actions {
        bottom: 20px;
        right: 20px;
        gap: 10px;
    }

    .back-to-top {
        width: 45px;
        height: 45px;
    }

    .whatsapp-float {
        width: 50px;
        height: 50px;
    }

    .phone-float {
        width: 45px;
        height: 45px;
    }

    .cookie-banner .row {
        text-align: center;
    }

    .cookie-banner .col-md-4 {
        margin-top: 1rem;
    }

    .footer-links-bottom {
        text-align: center !important;
        margin-bottom: 1rem;
    }

    .footer-links-bottom a {
        display: block;
        margin: 0.5rem 0;
    }

    .payment-icons {
        text-align: center;
    }

    .newsletter-form {
        max-width: 300px;
        margin: 0 auto;
    }
}

/* Animation for footer elements */
.footer-links li,
.contact-item,
.social-link {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.footer-links li:nth-child(1) { animation-delay: 0.1s; }
.footer-links li:nth-child(2) { animation-delay: 0.2s; }
.footer-links li:nth-child(3) { animation-delay: 0.3s; }
.footer-links li:nth-child(4) { animation-delay: 0.4s; }
.footer-links li:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
// Global footer functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Back to top functionality
    initBackToTop();

    // Business hours status
    updateBusinessStatus();
    setInterval(updateBusinessStatus, 60000); // Update every minute

    // Newsletter form
    initNewsletterForm();

    // Cookie banner
    initCookieBanner();

    // Floating buttons visibility
    initFloatingButtons();
});

// Back to top functionality
function initBackToTop() {
    const backToTopBtn = document.querySelector('.back-to-top');

    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }
    });

    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Update business status
function updateBusinessStatus() {
    const statusElement = document.getElementById('businessStatus');
    const statusText = document.getElementById('statusText');

    if (!statusElement || !statusText) return;

    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Business hours: Monday-Saturday 8:00-17:00
    const isBusinessDay = currentDay >= 1 && currentDay <= 6;
    const isBusinessHour = currentHour >= 8 && currentHour < 17;
    const isOpen = isBusinessDay && isBusinessHour;

    if (isOpen) {
        statusElement.className = 'status-indicator open';
        statusText.textContent = 'Buka Sekarang';
    } else {
        statusElement.className = 'status-indicator closed';
        statusText.textContent = 'Tutup';
    }
}

// Newsletter form functionality
function initNewsletterForm() {
    const newsletterForm = document.getElementById('newsletterForm');

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = this.querySelector('input[type="email"]').value;
            const button = this.querySelector('button');
            const originalHTML = button.innerHTML;

            // Show loading
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;

            // Simulate API call
            setTimeout(() => {
                showNotification('Terima kasih! Anda telah berlangganan newsletter kami.', 'success');
                this.reset();
                button.innerHTML = originalHTML;
                button.disabled = false;
            }, 1500);
        });
    }
}

// Cookie banner functionality
function initCookieBanner() {
    const cookieBanner = document.getElementById('cookieBanner');

    // Check if user has already made a choice
    if (!localStorage.getItem('cookieConsent')) {
        setTimeout(() => {
            cookieBanner.classList.add('show');
        }, 2000);
    }
}

function acceptCookies() {
    localStorage.setItem('cookieConsent', 'accepted');
    document.getElementById('cookieBanner').classList.remove('show');
    showNotification('Pengaturan cookie telah disimpan.', 'success');
}

function declineCookies() {
    localStorage.setItem('cookieConsent', 'declined');
    document.getElementById('cookieBanner').classList.remove('show');
    showNotification('Anda telah menolak penggunaan cookies.', 'info');
}

// Floating buttons visibility
function initFloatingButtons() {
    const floatingActions = document.querySelector('.floating-actions');

    window.addEventListener('scroll', function() {
        if (window.scrollY > 200) {
            floatingActions.style.opacity = '1';
            floatingActions.style.visibility = 'visible';
        } else {
            floatingActions.style.opacity = '0.7';
        }
    });
}

// WhatsApp helper function
function openWhatsApp(message) {
    const phoneNumber = '<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>';
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
}

// Show notification
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification alert alert-${type} alert-dismissible fade show`;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    `;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Show sitemap
function showSitemap() {
    const sitemapContent = `
        <div class="row">
            <div class="col-md-6">
                <h6>Halaman Utama</h6>
                <ul class="list-unstyled">
                    <li><a href="index.php?page=home">Beranda</a></li>
                    <li><a href="index.php?page=products">Produk</a></li>
                    <li><a href="index.php?page=profile">Profil Usaha</a></li>
                    <li><a href="index.php?page=gallery">Galeri & Testimoni</a></li>
                    <li><a href="index.php?page=contact">Kontak Kami</a></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Kategori Produk</h6>
                <ul class="list-unstyled">
                    <li><a href="index.php?page=products&category=air_isi_ulang">Air Isi Ulang</a></li>
                    <li><a href="index.php?page=products&category=air_kemasan">Air Kemasan</a></li>
                </ul>
                <h6 class="mt-3">Lainnya</h6>
                <ul class="list-unstyled">
                    <li><a href="admin/">Admin Panel</a></li>
                </ul>
            </div>
        </div>
    `;

    // Create and show modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-sitemap me-2"></i>
                        Peta Situs
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${sitemapContent}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Remove modal after hiding
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// Loading overlay functions
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// Print functionality
function printPage() {
    window.print();
}

// Share functionality
function shareWebsite() {
    if (navigator.share) {
        navigator.share({
            title: 'Agen Air Fazza',
            text: 'Air Bersih Berkualitas untuk Keluarga Sehat',
            url: window.location.href
        });
    } else {
        // Fallback: copy URL to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            showNotification('Link website berhasil disalin ke clipboard!', 'success');
        });
    }
}
</script>
