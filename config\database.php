<?php
/**
 * Database Configuration for Agen Air Fazza
 * This file handles database connection and setup
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'sim_airfazza');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Create database connection
try {
    // First, try to connect to the specific database
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
    
} catch(PDOException $e) {
    // If database doesn't exist, try to create it
    try {
        $dsn_temp = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
        $pdo_temp = new PDO($dsn_temp, DB_USER, DB_PASS);
        $pdo_temp->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo_temp->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET " . DB_CHARSET . " COLLATE utf8mb4_unicode_ci");
        
        // Now connect to the created database
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        
        // Check if tables exist, if not, run setup
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        if (empty($tables)) {
            // Include the setup script
            include_once __DIR__ . '/setup-tables.php';
            setupDatabase($pdo);
        }
        
    } catch(PDOException $e) {
        // Show user-friendly error message
        $error_message = "Database connection failed. Please check your database configuration.";
        if (defined('DEBUG') && DEBUG) {
            $error_message .= "<br>Error: " . $e->getMessage();
        }
        die($error_message);
    }
}

// Test connection
try {
    $pdo->query("SELECT 1");
} catch(PDOException $e) {
    die("Database connection test failed: " . $e->getMessage());
}
?>
