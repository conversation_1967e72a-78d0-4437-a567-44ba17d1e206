<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, maximum-scale=5.0">
    <title>Responsive Test - Agen Air Fazza</title>
    
    <!-- Mobile-specific meta tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#2563eb">
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/global.css" rel="stylesheet">
    <link href="assets/css/mobile.css" rel="stylesheet">
    <link href="assets/css/responsive-utils.css" rel="stylesheet">
    
    <style>
        .test-section {
            padding: 2rem 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .test-header {
            background: linear-gradient(135deg, #2563eb, #06b6d4);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }
        
        .breakpoint-indicator {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            z-index: 9999;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .test-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .touch-test {
            min-height: 44px;
            min-width: 44px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 0.25rem;
            margin: 0.25rem;
            cursor: pointer;
        }
        
        .touch-test:active {
            transform: scale(0.95);
        }
        
        .responsive-text {
            font-size: clamp(1rem, 2.5vw, 1.5rem);
        }
        
        .mobile-only {
            display: none;
        }
        
        .desktop-only {
            display: block;
        }
        
        @media (max-width: 767.98px) {
            .mobile-only {
                display: block;
            }
            
            .desktop-only {
                display: none;
            }
        }
    </style>
</head>
<body class="debug-breakpoints">
    <!-- Breakpoint Indicator -->
    <div class="breakpoint-indicator" id="breakpointIndicator">
        Loading...
    </div>
    
    <!-- Header -->
    <div class="test-header">
        <div class="container">
            <h1 class="fluid-heading">Responsive Design Test</h1>
            <p class="fluid-text">Testing mobile optimization and responsive features</p>
        </div>
    </div>
    
    <!-- Navigation Test -->
    <div class="test-section">
        <div class="container">
            <h2>Navigation Test</h2>
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <a class="navbar-brand" href="#">
                        <i class="fas fa-tint me-2"></i>
                        Air Fazza
                    </a>
                    
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#testNav">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    
                    <div class="collapse navbar-collapse" id="testNav">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link touch-target" href="#">
                                    <i class="fas fa-home me-2"></i>
                                    Beranda
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link touch-target" href="#">
                                    <i class="fas fa-box me-2"></i>
                                    Produk
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link touch-target" href="#">
                                    <i class="fas fa-envelope me-2"></i>
                                    Kontak
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
    </div>
    
    <!-- Typography Test -->
    <div class="test-section">
        <div class="container">
            <h2>Typography Test</h2>
            <div class="row">
                <div class="col-md-6">
                    <h3>Responsive Headings</h3>
                    <h1 class="fluid-heading">Fluid Heading H1</h1>
                    <h2 class="fluid-subheading">Fluid Subheading H2</h2>
                    <p class="fluid-text">This is fluid text that scales with viewport width.</p>
                    <p class="responsive-text">This text uses clamp() for responsive sizing.</p>
                </div>
                <div class="col-md-6">
                    <h3>Device-specific Content</h3>
                    <div class="mobile-only alert alert-info">
                        <i class="fas fa-mobile-alt me-2"></i>
                        This content only shows on mobile devices
                    </div>
                    <div class="desktop-only alert alert-success">
                        <i class="fas fa-desktop me-2"></i>
                        This content only shows on desktop devices
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Touch Target Test -->
    <div class="test-section">
        <div class="container">
            <h2>Touch Target Test</h2>
            <p>All buttons below should be at least 44x44px for optimal touch interaction:</p>
            <div class="d-flex flex-wrap">
                <button class="touch-test">Button 1</button>
                <button class="touch-test">Button 2</button>
                <button class="touch-test">Button 3</button>
                <button class="touch-test">Button 4</button>
                <button class="touch-test">Button 5</button>
            </div>
        </div>
    </div>
    
    <!-- Grid Test -->
    <div class="test-section">
        <div class="container">
            <h2>Responsive Grid Test</h2>
            <div class="test-grid">
                <div class="test-card">
                    <h4>Card 1</h4>
                    <p>This grid adapts to different screen sizes using CSS Grid with auto-fit and minmax.</p>
                </div>
                <div class="test-card">
                    <h4>Card 2</h4>
                    <p>On mobile, cards stack vertically. On larger screens, they arrange in multiple columns.</p>
                </div>
                <div class="test-card">
                    <h4>Card 3</h4>
                    <p>The grid maintains optimal readability across all device sizes.</p>
                </div>
                <div class="test-card">
                    <h4>Card 4</h4>
                    <p>Each card has a minimum width to ensure content remains readable.</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Form Test -->
    <div class="test-section">
        <div class="container">
            <h2>Mobile Form Test</h2>
            <div class="row">
                <div class="col-md-6">
                    <form>
                        <div class="mb-3">
                            <label for="testEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="testEmail" placeholder="<EMAIL>">
                        </div>
                        <div class="mb-3">
                            <label for="testPhone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="testPhone" placeholder="+62 812 3456 7890">
                        </div>
                        <div class="mb-3">
                            <label for="testMessage" class="form-label">Message</label>
                            <textarea class="form-control" id="testMessage" rows="3" placeholder="Your message here..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary touch-target w-100 w-md-auto">
                            <i class="fas fa-paper-plane me-2"></i>
                            Send Message
                        </button>
                    </form>
                </div>
                <div class="col-md-6">
                    <h4>Form Features:</h4>
                    <ul>
                        <li>16px font size to prevent zoom on iOS</li>
                        <li>Touch-friendly button sizes</li>
                        <li>Full-width buttons on mobile</li>
                        <li>Proper input types for mobile keyboards</li>
                        <li>Accessible labels and placeholders</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Performance Test -->
    <div class="test-section">
        <div class="container">
            <h2>Performance Test</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="test-card">
                        <h4>Image Loading</h4>
                        <img src="https://via.placeholder.com/300x200/2563eb/ffffff?text=Lazy+Load" 
                             alt="Test Image" class="img-fluid" loading="lazy">
                        <p class="mt-2">This image uses lazy loading for better performance.</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="test-card">
                        <h4>Animations</h4>
                        <div class="animate-mobile" style="background: #f3f4f6; padding: 1rem; border-radius: 0.25rem;">
                            Animations are optimized for mobile devices
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="test-card">
                        <h4>PWA Features</h4>
                        <button onclick="testPWA()" class="btn btn-outline-primary">
                            Test PWA Features
                        </button>
                        <div id="pwaResults" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Device Information -->
    <div class="test-section">
        <div class="container">
            <h2>Device Information</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="test-card">
                        <h4>Screen Information</h4>
                        <div id="screenInfo"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="test-card">
                        <h4>Browser Features</h4>
                        <div id="browserFeatures"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/mobile.js"></script>
    
    <script>
        // Update breakpoint indicator
        function updateBreakpointIndicator() {
            const indicator = document.getElementById('breakpointIndicator');
            const width = window.innerWidth;
            let breakpoint = '';
            
            if (width < 576) {
                breakpoint = 'XS (< 576px)';
            } else if (width < 768) {
                breakpoint = 'SM (576px - 767px)';
            } else if (width < 992) {
                breakpoint = 'MD (768px - 991px)';
            } else if (width < 1200) {
                breakpoint = 'LG (992px - 1199px)';
            } else if (width < 1400) {
                breakpoint = 'XL (1200px - 1399px)';
            } else {
                breakpoint = 'XXL (≥ 1400px)';
            }
            
            indicator.textContent = `${breakpoint} | ${width}px`;
        }
        
        // Update screen information
        function updateScreenInfo() {
            const screenInfo = document.getElementById('screenInfo');
            screenInfo.innerHTML = `
                <strong>Screen Size:</strong> ${screen.width} x ${screen.height}<br>
                <strong>Viewport:</strong> ${window.innerWidth} x ${window.innerHeight}<br>
                <strong>Device Pixel Ratio:</strong> ${window.devicePixelRatio}<br>
                <strong>Orientation:</strong> ${window.innerWidth > window.innerHeight ? 'Landscape' : 'Portrait'}
            `;
        }
        
        // Update browser features
        function updateBrowserFeatures() {
            const features = document.getElementById('browserFeatures');
            features.innerHTML = `
                <strong>Touch Support:</strong> ${'ontouchstart' in window ? 'Yes' : 'No'}<br>
                <strong>Service Worker:</strong> ${'serviceWorker' in navigator ? 'Yes' : 'No'}<br>
                <strong>Geolocation:</strong> ${'geolocation' in navigator ? 'Yes' : 'No'}<br>
                <strong>Local Storage:</strong> ${typeof(Storage) !== "undefined" ? 'Yes' : 'No'}<br>
                <strong>Vibration:</strong> ${'vibrate' in navigator ? 'Yes' : 'No'}
            `;
        }
        
        // Test PWA features
        function testPWA() {
            const results = document.getElementById('pwaResults');
            let html = '<small>';
            
            if ('serviceWorker' in navigator) {
                html += '✅ Service Worker supported<br>';
            } else {
                html += '❌ Service Worker not supported<br>';
            }
            
            if (window.matchMedia('(display-mode: standalone)').matches) {
                html += '✅ Running as PWA<br>';
            } else {
                html += '📱 Running in browser<br>';
            }
            
            if ('Notification' in window) {
                html += '✅ Notifications supported<br>';
            } else {
                html += '❌ Notifications not supported<br>';
            }
            
            html += '</small>';
            results.innerHTML = html;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateBreakpointIndicator();
            updateScreenInfo();
            updateBrowserFeatures();
            
            // Update on resize
            window.addEventListener('resize', function() {
                updateBreakpointIndicator();
                updateScreenInfo();
            });
            
            // Update on orientation change
            window.addEventListener('orientationchange', function() {
                setTimeout(function() {
                    updateBreakpointIndicator();
                    updateScreenInfo();
                }, 100);
            });
        });
    </script>
</body>
</html>
