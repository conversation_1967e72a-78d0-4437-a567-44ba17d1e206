/* Mobile Optimization & Responsive Design */
/* Enhanced mobile-first responsive styles for Agen Air Fazza */

/* ===== MOBILE-FIRST BASE STYLES ===== */

/* Touch-friendly interactions */
* {
    -webkit-tap-highlight-color: rgba(37, 99, 235, 0.2);
    -webkit-touch-callout: none;
}

/* Improved touch targets */
button, .btn, a, input, select, textarea {
    min-height: 44px;
    min-width: 44px;
}

/* Smooth scrolling for mobile */
html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

/* Prevent horizontal scroll */
body {
    overflow-x: hidden;
}

/* ===== MOBILE BREAKPOINTS ===== */

/* Extra Small devices (phones, 576px and down) */
@media (max-width: 575.98px) {
    /* Typography adjustments */
    .display-1 { font-size: 2.5rem; }
    .display-2 { font-size: 2rem; }
    .display-3 { font-size: 1.75rem; }
    .display-4 { font-size: 1.5rem; }
    
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    
    /* Container adjustments */
    .container, .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    /* Section spacing */
    .section {
        padding: 3rem 0;
    }
    
    /* Hero section mobile */
    .hero-section {
        min-height: 70vh;
        padding: 2rem 0;
    }
    
    .hero-content h1 {
        font-size: 2rem;
        line-height: 1.2;
    }
    
    .hero-content .lead {
        font-size: 1rem;
    }
    
    /* Button adjustments */
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .btn-group-vertical .btn {
        margin-bottom: 0.5rem;
    }
    
    /* Card adjustments */
    .card {
        margin-bottom: 1.5rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
    
    /* Navigation mobile */
    .navbar-nav .nav-link {
        padding: 1rem;
        font-size: 1.1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .navbar-collapse {
        background: rgba(0, 0, 0, 0.95);
        margin: 0 -1rem;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-top: 1rem;
    }
    
    /* Header adjustments */
    .top-bar {
        display: none;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    /* Service cards mobile */
    .service-card {
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    /* Product cards mobile */
    .product-card {
        margin-bottom: 2rem;
    }
    
    .product-image {
        height: 200px;
    }
    
    /* Gallery mobile */
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .gallery-image-container {
        height: 200px;
    }
    
    /* Testimonial mobile */
    .testimonial-card {
        margin-bottom: 2rem;
        padding: 1.5rem;
    }
    
    .testimonial-header {
        flex-direction: column;
        text-align: center;
    }
    
    .testimonial-avatar {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    /* Contact form mobile */
    .contact-form-container {
        padding: 1.5rem;
    }
    
    .contact-info-card {
        margin-bottom: 1.5rem;
        padding: 1.5rem;
    }
    
    /* Footer mobile */
    .footer .row > div {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .footer-links-bottom a {
        display: block;
        margin: 0.5rem 0;
    }
    
    /* Floating actions mobile */
    .floating-actions {
        bottom: 20px;
        right: 15px;
        gap: 10px;
    }
    
    .back-to-top,
    .whatsapp-float,
    .phone-float {
        width: 50px;
        height: 50px;
    }
    
    .whatsapp-float {
        width: 55px;
        height: 55px;
    }
    
    /* Modal mobile */
    .modal-dialog {
        margin: 1rem;
    }
    
    .modal-content {
        border-radius: 1rem;
    }
    
    /* Table mobile */
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
    }
    
    /* Form mobile */
    .form-control,
    .form-select {
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    /* Accordion mobile */
    .accordion-button {
        padding: 1rem;
        font-size: 0.9rem;
    }
    
    .accordion-body {
        padding: 1rem;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .service-card {
        margin-bottom: 1.5rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .floating-actions {
        bottom: 25px;
        right: 20px;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .hero-content h1 {
        font-size: 3rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .testimonial-header {
        flex-direction: row;
        text-align: left;
    }
    
    .testimonial-avatar {
        margin-right: 1rem;
        margin-bottom: 0;
    }
    
    .contact-info-card {
        margin-bottom: 2rem;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* ===== MOBILE-SPECIFIC ENHANCEMENTS ===== */

/* Touch gestures */
.swipe-container {
    touch-action: pan-x;
}

/* Mobile navigation enhancements */
@media (max-width: 991.98px) {
    .navbar-toggler {
        border: none;
        padding: 0.5rem;
    }
    
    .navbar-toggler:focus {
        box-shadow: none;
    }
    
    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    }
    
    .navbar-nav {
        padding-top: 1rem;
    }
    
    .navbar-nav .nav-item {
        margin-bottom: 0.5rem;
    }
    
    .navbar-nav .nav-link {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.5rem;
        margin-bottom: 0.25rem;
        transition: all 0.3s ease;
    }
    
    .navbar-nav .nav-link:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateX(5px);
    }
}

/* Mobile-optimized images */
@media (max-width: 767.98px) {
    img {
        max-width: 100%;
        height: auto;
    }
    
    .img-fluid {
        width: 100%;
    }
    
    /* Lazy loading optimization */
    img[loading="lazy"] {
        transition: opacity 0.3s ease;
    }
}

/* Mobile form optimizations */
@media (max-width: 767.98px) {
    .form-floating > label {
        font-size: 0.875rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .input-group > * {
        border-radius: 0.375rem !important;
        margin-bottom: 0.5rem;
    }
    
    .input-group > *:last-child {
        margin-bottom: 0;
    }
}

/* Mobile card optimizations */
@media (max-width: 767.98px) {
    .card-deck {
        flex-direction: column;
    }
    
    .card-deck .card {
        margin-bottom: 1rem;
    }
    
    .card-columns {
        column-count: 1;
    }
}

/* Mobile utility classes */
.mobile-only {
    display: none;
}

.desktop-only {
    display: block;
}

@media (max-width: 767.98px) {
    .mobile-only {
        display: block;
    }
    
    .desktop-only {
        display: none;
    }
    
    .mobile-center {
        text-align: center !important;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
    
    .mobile-no-padding {
        padding: 0 !important;
    }
    
    .mobile-small-padding {
        padding: 0.5rem !important;
    }
}

/* Performance optimizations for mobile */
@media (max-width: 767.98px) {
    /* Reduce animations on mobile for better performance */
    .reduce-motion * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    /* Optimize background images for mobile */
    .bg-image-mobile {
        background-attachment: scroll !important;
        background-size: cover;
    }
}

/* Accessibility improvements for mobile */
@media (max-width: 767.98px) {
    /* Larger focus indicators */
    *:focus {
        outline: 3px solid var(--primary-color);
        outline-offset: 2px;
    }
    
    /* Better contrast for mobile */
    .text-muted {
        color: #495057 !important;
    }
}

/* Print optimizations */
@media print {
    .mobile-only,
    .floating-actions,
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
        width: 100% !important;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
