/* Global CSS - Additional styles for enhanced user experience */

/* Smooth scrolling for all browsers */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Selection color */
::selection {
    background: var(--primary-color);
    color: white;
}

::-moz-selection {
    background: var(--primary-color);
    color: white;
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.25);
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Skeleton loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Image lazy loading */
img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s;
}

img[loading="lazy"].loaded {
    opacity: 1;
}

/* Print styles */
@media print {
    .no-print,
    .floating-actions,
    .cookie-banner,
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }
    
    .container {
        max-width: none !important;
        width: 100% !important;
    }
    
    .page-header {
        background: none !important;
        color: black !important;
    }
    
    a {
        color: black !important;
        text-decoration: none !important;
    }
    
    .footer {
        background: none !important;
        color: black !important;
        border-top: 1px solid #ccc;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --text-dark: #000000;
        --text-light: #333333;
        --border-color: #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    .auto-dark {
        --bg-white: #1a1a1a;
        --bg-light: #2a2a2a;
        --text-dark: #ffffff;
        --text-light: #cccccc;
        --border-color: #444444;
    }
}

/* Utility classes */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.box-shadow-sm {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.box-shadow-md {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.box-shadow-lg {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hover-lift {
    transition: transform 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
}

.fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

.slide-up {
    transform: translateY(30px);
    opacity: 0;
    animation: slideUp 0.6s ease-out forwards;
}

@keyframes slideUp {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Interactive elements */
.interactive {
    cursor: pointer;
    transition: all 0.3s ease;
}

.interactive:hover {
    transform: scale(1.05);
}

.interactive:active {
    transform: scale(0.95);
}

/* Status indicators */
.status-online {
    color: #10b981;
}

.status-offline {
    color: #ef4444;
}

.status-busy {
    color: #f59e0b;
}

/* Notification styles */
.notification {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.notification.alert-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
}

.notification.alert-info {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
}

.notification.alert-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border: none;
}

.notification.alert-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border: none;
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Skip to content link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    z-index: 10000;
}

.skip-link:focus {
    top: 0;
}

/* Performance optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* Browser-specific fixes */
/* Safari */
@supports (-webkit-appearance: none) {
    .safari-fix {
        -webkit-transform: translateZ(0);
    }
}

/* Firefox */
@-moz-document url-prefix() {
    .firefox-fix {
        -moz-transform: translateZ(0);
    }
}
