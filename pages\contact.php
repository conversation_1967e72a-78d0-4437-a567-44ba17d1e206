<?php
$company_info = getCompanyInfo();
?>

<!-- Page Header -->
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row align-items-center py-5">
            <div class="col-lg-8" data-aos="fade-right">
                <h1 class="display-4 fw-bold mb-3">Kontak Kami</h1>
                <p class="lead mb-0">
                    Hubungi kami untuk informasi lebih lanjut, pemesanan, atau pertanyaan seputar 
                    produk dan layanan Agen Air Fazza. Tim kami siap melayani Anda.
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <div class="page-header-icon">
                    <i class="fas fa-envelope display-1 opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information Section -->
<section class="contact-info-section section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">Informasi Kontak</h2>
                <p class="lead text-muted">
                    Berbagai cara untuk menghubungi kami dengan mudah dan cepat
                </p>
            </div>
        </div>
        
        <div class="row">
            <!-- Address -->
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="contact-info-card">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="contact-details">
                        <h5>Alamat Kami</h5>
                        <p><?php echo $company_info['company_address']; ?></p>
                        <a href="https://maps.google.com/?q=<?php echo urlencode($company_info['company_address']); ?>" 
                           class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-directions me-1"></i>
                            Lihat di Maps
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Phone -->
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="contact-info-card">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="contact-details">
                        <h5>Telepon</h5>
                        <p><?php echo $company_info['company_phone']; ?></p>
                        <a href="tel:<?php echo $company_info['company_phone']; ?>" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-phone me-1"></i>
                            Telepon Sekarang
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- WhatsApp -->
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="contact-info-card">
                    <div class="contact-icon whatsapp">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="contact-details">
                        <h5>WhatsApp</h5>
                        <p><?php echo $company_info['company_whatsapp']; ?></p>
                        <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>?text=Halo, saya ingin bertanya tentang produk Agen Air Fazza" 
                           class="btn btn-success btn-sm" target="_blank">
                            <i class="fab fa-whatsapp me-1"></i>
                            Chat WhatsApp
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Email -->
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="400">
                <div class="contact-info-card">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-details">
                        <h5>Email</h5>
                        <p><?php echo $company_info['company_email']; ?></p>
                        <a href="mailto:<?php echo $company_info['company_email']; ?>" 
                           class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope me-1"></i>
                            Kirim Email
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Business Hours -->
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="500">
                <div class="contact-info-card">
                    <div class="contact-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="contact-details">
                        <h5>Jam Operasional</h5>
                        <p><?php echo $company_info['business_hours']; ?></p>
                        <div class="status-badge">
                            <span class="badge bg-success">
                                <i class="fas fa-circle me-1"></i>
                                Buka Sekarang
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Emergency Contact -->
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="600">
                <div class="contact-info-card">
                    <div class="contact-icon emergency">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="contact-details">
                        <h5>Kontak Darurat</h5>
                        <p>Untuk kebutuhan mendesak</p>
                        <a href="tel:<?php echo $company_info['company_phone']; ?>" 
                           class="btn btn-danger btn-sm">
                            <i class="fas fa-phone me-1"></i>
                            Hubungi Darurat
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="contact-form-section section bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">Kirim Pesan</h2>
                <p class="lead text-muted">
                    Isi formulir di bawah ini untuk mengirim pesan langsung kepada kami. 
                    Kami akan merespons dalam waktu 24 jam.
                </p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="contact-form-container" data-aos="fade-up">
                    <form id="contactForm" class="contact-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Nama Lengkap *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Nomor Telepon</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                                <div class="invalid-feedback"></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">Subjek</label>
                                <select class="form-control" id="subject" name="subject">
                                    <option value="">Pilih Subjek</option>
                                    <option value="Informasi Produk">Informasi Produk</option>
                                    <option value="Pemesanan">Pemesanan</option>
                                    <option value="Keluhan">Keluhan</option>
                                    <option value="Saran">Saran</option>
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Pesan *</label>
                            <textarea class="form-control" id="message" name="message" rows="5" 
                                      placeholder="Tulis pesan Anda di sini..." required></textarea>
                            <div class="invalid-feedback"></div>
                            <div class="form-text">Minimal 10 karakter</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="privacy" name="privacy" required>
                                <label class="form-check-label" for="privacy">
                                    Saya setuju dengan <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">kebijakan privasi</a> *
                                </label>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>
                                Kirim Pesan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="map-section">
    <div class="container-fluid p-0">
        <div class="row g-0">
            <div class="col-lg-8">
                <div class="map-container" data-aos="fade-right">
                    <div id="map" class="map-embed">
                        <!-- Google Maps Embed -->
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3966.************!2d106.8195613!3d-6.2087634!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e69f5390917b759%3A0x6b45e67356080477!2sJakarta%2C%20Indonesia!5e0!3m2!1sen!2sid!4v1635123456789!5m2!1sen!2sid" 
                                width="100%" 
                                height="400" 
                                style="border:0;" 
                                allowfullscreen="" 
                                loading="lazy" 
                                referrerpolicy="no-referrer-when-downgrade">
                        </iframe>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="map-info" data-aos="fade-left">
                    <h4 class="mb-4">Lokasi Kami</h4>
                    <div class="location-details">
                        <div class="location-item mb-3">
                            <i class="fas fa-map-marker-alt text-primary me-3"></i>
                            <div>
                                <strong>Alamat:</strong><br>
                                <?php echo $company_info['company_address']; ?>
                            </div>
                        </div>
                        <div class="location-item mb-3">
                            <i class="fas fa-car text-primary me-3"></i>
                            <div>
                                <strong>Akses:</strong><br>
                                Mudah dijangkau dengan kendaraan pribadi atau transportasi umum
                            </div>
                        </div>
                        <div class="location-item mb-4">
                            <i class="fas fa-parking text-primary me-3"></i>
                            <div>
                                <strong>Parkir:</strong><br>
                                Tersedia area parkir yang luas dan aman
                            </div>
                        </div>
                        <a href="https://maps.google.com/?q=<?php echo urlencode($company_info['company_address']); ?>" 
                           class="btn btn-primary w-100" target="_blank">
                            <i class="fas fa-directions me-2"></i>
                            Dapatkan Petunjuk Arah
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">Pertanyaan yang Sering Diajukan</h2>
                <p class="lead text-muted">
                    Temukan jawaban untuk pertanyaan yang paling sering ditanyakan pelanggan
                </p>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="faqAccordion" data-aos="fade-up">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1">
                                <i class="fas fa-question-circle me-2"></i>
                                Bagaimana cara memesan air isi ulang?
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse show"
                             aria-labelledby="faq1" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Anda dapat memesan melalui WhatsApp di <?php echo $company_info['company_whatsapp']; ?>
                                atau telepon ke <?php echo $company_info['company_phone']; ?>.
                                Tim kami akan mengatur jadwal pengiriman sesuai kebutuhan Anda.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapse2" aria-expanded="false" aria-controls="collapse2">
                                <i class="fas fa-truck me-2"></i>
                                Apakah ada biaya pengiriman?
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse"
                             aria-labelledby="faq2" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Untuk pemesanan dalam area tertentu, kami menyediakan layanan antar gratis.
                                Untuk area di luar jangkauan, akan dikenakan biaya pengiriman yang terjangkau.
                                Hubungi kami untuk informasi detail area pengiriman.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapse3" aria-expanded="false" aria-controls="collapse3">
                                <i class="fas fa-shield-alt me-2"></i>
                                Bagaimana kualitas air dijamin?
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse"
                             aria-labelledby="faq3" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Semua produk kami telah melewati proses penyaringan berlapis, sterilisasi UV,
                                dan pengujian kualitas berkala. Kami memiliki sertifikasi SNI dan BPOM
                                untuk menjamin keamanan dan kualitas air yang kami produksi.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq4">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapse4" aria-expanded="false" aria-controls="collapse4">
                                <i class="fas fa-clock me-2"></i>
                                Berapa lama waktu pengiriman?
                            </button>
                        </h2>
                        <div id="collapse4" class="accordion-collapse collapse"
                             aria-labelledby="faq4" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Untuk area dalam kota, pengiriman biasanya dilakukan dalam 2-4 jam setelah pemesanan.
                                Untuk area luar kota, waktu pengiriman 1-2 hari kerja.
                                Kami juga melayani pemesanan dengan jadwal rutin harian atau mingguan.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq5">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapse5" aria-expanded="false" aria-controls="collapse5">
                                <i class="fas fa-credit-card me-2"></i>
                                Metode pembayaran apa saja yang tersedia?
                            </button>
                        </h2>
                        <div id="collapse5" class="accordion-collapse collapse"
                             aria-labelledby="faq5" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Kami menerima pembayaran tunai, transfer bank, e-wallet (OVO, GoPay, DANA),
                                dan QRIS. Untuk pelanggan berlangganan, tersedia sistem pembayaran bulanan
                                yang lebih praktis dan hemat.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8" data-aos="fade-right">
                <h2 class="mb-3">Masih Ada Pertanyaan?</h2>
                <p class="lead mb-0">
                    Tim customer service kami siap membantu Anda 24/7.
                    Jangan ragu untuk menghubungi kami kapan saja!
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <div class="cta-buttons">
                    <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', $company_info['company_whatsapp']); ?>?text=Halo, saya memiliki pertanyaan tentang layanan Agen Air Fazza"
                       class="btn btn-light btn-lg mb-2 me-2" target="_blank">
                        <i class="fab fa-whatsapp me-2"></i>
                        Chat Sekarang
                    </a>
                    <a href="tel:<?php echo $company_info['company_phone']; ?>"
                       class="btn btn-outline-light btn-lg mb-2">
                        <i class="fas fa-phone me-2"></i>
                        Telepon
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Privacy Policy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1" aria-labelledby="privacyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="privacyModalLabel">
                    <i class="fas fa-shield-alt me-2"></i>
                    Kebijakan Privasi
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>Pengumpulan Informasi</h6>
                <p>Kami mengumpulkan informasi yang Anda berikan secara sukarela melalui formulir kontak,
                   termasuk nama, email, nomor telepon, dan pesan Anda.</p>

                <h6>Penggunaan Informasi</h6>
                <p>Informasi yang dikumpulkan digunakan untuk:</p>
                <ul>
                    <li>Merespons pertanyaan dan permintaan Anda</li>
                    <li>Memberikan informasi produk dan layanan</li>
                    <li>Meningkatkan kualitas layanan kami</li>
                </ul>

                <h6>Perlindungan Data</h6>
                <p>Kami berkomitmen melindungi informasi pribadi Anda dan tidak akan membagikannya
                   kepada pihak ketiga tanpa persetujuan Anda.</p>

                <h6>Kontak</h6>
                <p>Jika Anda memiliki pertanyaan tentang kebijakan privasi ini,
                   silakan hubungi kami di <?php echo $company_info['company_email']; ?></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Saya Mengerti</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

/* Contact Info Cards */
.contact-info-card {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100%;
    text-align: center;
}

.contact-info-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.contact-icon.whatsapp {
    background: linear-gradient(135deg, #25d366, #128c7e);
}

.contact-icon.emergency {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.contact-details h5 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.contact-details p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.status-badge {
    margin-top: 1rem;
}

/* Contact Form Styles */
.contact-form-container {
    background: white;
    padding: 3rem;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.contact-form .form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.contact-form .form-control {
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.contact-form .form-control.is-invalid {
    border-color: #dc3545;
}

.contact-form .form-control.is-valid {
    border-color: #28a745;
}

.contact-form .invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
}

.contact-form .form-text {
    font-size: 0.875rem;
    color: var(--text-light);
}

.contact-form .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Map Section Styles */
.map-section {
    background: var(--bg-light);
}

.map-container {
    position: relative;
    height: 400px;
}

.map-embed {
    width: 100%;
    height: 100%;
    border-radius: 0;
}

.map-info {
    background: white;
    padding: 3rem;
    height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.location-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.location-item i {
    margin-top: 0.25rem;
    width: 20px;
}

/* FAQ Styles */
.accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 0.5rem !important;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.accordion-button {
    background: white;
    border: none;
    padding: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    border-radius: 0.5rem !important;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-button:not(.collapsed)::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-body {
    padding: 1.5rem;
    background: #f8f9fa;
    color: var(--text-light);
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header .display-4 {
        font-size: 2rem;
    }

    .contact-info-card {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .contact-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .contact-form-container {
        padding: 2rem 1.5rem;
    }

    .map-info {
        padding: 2rem;
        height: auto;
    }

    .map-container {
        height: 300px;
    }

    .accordion-button {
        padding: 1rem;
        font-size: 0.9rem;
    }

    .accordion-body {
        padding: 1rem;
    }

    .cta-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
        margin-right: 0 !important;
    }
}

/* Animation for contact cards */
.contact-info-card {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading animation for form submission */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spinner.fa-spin {
    animation: spin 1s linear infinite;
}

/* Custom scrollbar for textarea */
.form-control::-webkit-scrollbar {
    width: 8px;
}

.form-control::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.form-control::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.form-control::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Print styles */
@media print {
    .contact-form-container,
    .cta-buttons,
    .btn {
        display: none !important;
    }

    .contact-info-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
</style>

<script>
// Contact form functionality
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');
    const formInputs = contactForm.querySelectorAll('input, textarea, select');

    // Real-time validation
    formInputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateField(this);
            }
        });
    });

    // Form submission
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate all fields
        let isValid = true;
        formInputs.forEach(input => {
            if (!validateField(input)) {
                isValid = false;
            }
        });

        if (isValid) {
            submitForm();
        } else {
            showAlert('error', 'Mohon perbaiki kesalahan pada formulir');
            // Scroll to first error
            const firstError = contactForm.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        }
    });

    // Initialize business hours status
    updateBusinessHoursStatus();
    setInterval(updateBusinessHoursStatus, 60000); // Update every minute
});

// Field validation function
function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name;
    let isValid = true;
    let errorMessage = '';

    // Remove existing validation classes
    field.classList.remove('is-valid', 'is-invalid');

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'Field ini wajib diisi';
    }

    // Specific field validations
    if (value && isValid) {
        switch (fieldName) {
            case 'name':
                if (value.length < 2) {
                    isValid = false;
                    errorMessage = 'Nama minimal 2 karakter';
                } else if (!/^[a-zA-Z\s]+$/.test(value)) {
                    isValid = false;
                    errorMessage = 'Nama hanya boleh berisi huruf dan spasi';
                }
                break;

            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'Format email tidak valid';
                }
                break;

            case 'phone':
                if (value && !/^[\+]?[0-9\s\-\(\)]{10,}$/.test(value)) {
                    isValid = false;
                    errorMessage = 'Format nomor telepon tidak valid';
                }
                break;

            case 'message':
                if (value.length < 10) {
                    isValid = false;
                    errorMessage = 'Pesan minimal 10 karakter';
                } else if (value.length > 1000) {
                    isValid = false;
                    errorMessage = 'Pesan maksimal 1000 karakter';
                }
                break;

            case 'privacy':
                if (!field.checked) {
                    isValid = false;
                    errorMessage = 'Anda harus menyetujui kebijakan privasi';
                }
                break;
        }
    }

    // Apply validation result
    if (isValid) {
        field.classList.add('is-valid');
        hideFieldError(field);
    } else {
        field.classList.add('is-invalid');
        showFieldError(field, errorMessage);
    }

    return isValid;
}

// Show field error
function showFieldError(field, message) {
    const feedback = field.parentElement.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.textContent = message;
        feedback.style.display = 'block';
    }
}

// Hide field error
function hideFieldError(field) {
    const feedback = field.parentElement.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.style.display = 'none';
    }
}

// Submit form
function submitForm() {
    const form = document.getElementById('contactForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mengirim...';
    submitBtn.disabled = true;

    // Prepare form data
    const formData = new FormData(form);

    // Send form data
    fetch('ajax/contact.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            form.reset();
            // Remove validation classes
            form.querySelectorAll('.is-valid, .is-invalid').forEach(el => {
                el.classList.remove('is-valid', 'is-invalid');
            });

            // Scroll to top of form
            form.scrollIntoView({ behavior: 'smooth', block: 'start' });
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Terjadi kesalahan. Silakan coba lagi atau hubungi kami langsung.');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Update business hours status
function updateBusinessHoursStatus() {
    const statusBadge = document.querySelector('.status-badge .badge');
    if (!statusBadge) return;

    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Business hours: Monday-Saturday 8:00-17:00
    const isBusinessDay = currentDay >= 1 && currentDay <= 6;
    const isBusinessHour = currentHour >= 8 && currentHour < 17;
    const isOpen = isBusinessDay && isBusinessHour;

    if (isOpen) {
        statusBadge.className = 'badge bg-success';
        statusBadge.innerHTML = '<i class="fas fa-circle me-1"></i>Buka Sekarang';
    } else {
        statusBadge.className = 'badge bg-danger';
        statusBadge.innerHTML = '<i class="fas fa-circle me-1"></i>Tutup';
    }
}

// Show alert function
function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());

    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert alert at the top of the form
    const form = document.getElementById('contactForm');
    form.insertBefore(alertDiv, form.firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Character counter for message field
document.addEventListener('DOMContentLoaded', function() {
    const messageField = document.getElementById('message');
    if (messageField) {
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        counter.style.fontSize = '0.8rem';
        messageField.parentElement.appendChild(counter);

        function updateCounter() {
            const length = messageField.value.length;
            const maxLength = 1000;
            counter.textContent = `${length}/${maxLength} karakter`;

            if (length > maxLength * 0.9) {
                counter.style.color = '#dc3545';
            } else if (length > maxLength * 0.7) {
                counter.style.color = '#ffc107';
            } else {
                counter.style.color = '#6c757d';
            }
        }

        messageField.addEventListener('input', updateCounter);
        updateCounter();
    }
});

// Auto-resize textarea
function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = textarea.scrollHeight + 'px';
}

document.addEventListener('DOMContentLoaded', function() {
    const messageTextarea = document.getElementById('message');
    if (messageTextarea) {
        messageTextarea.addEventListener('input', function() {
            autoResizeTextarea(this);
        });
    }
});

// Copy contact info to clipboard
function copyToClipboard(text, element) {
    navigator.clipboard.writeText(text).then(function() {
        // Show temporary success message
        const originalText = element.innerHTML;
        element.innerHTML = '<i class="fas fa-check me-1"></i>Disalin!';
        element.classList.add('btn-success');

        setTimeout(() => {
            element.innerHTML = originalText;
            element.classList.remove('btn-success');
        }, 2000);
    }).catch(function() {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        showAlert('success', 'Informasi berhasil disalin ke clipboard');
    });
}
</script>
