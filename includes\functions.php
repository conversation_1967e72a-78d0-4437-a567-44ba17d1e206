<?php
// Utility functions for the website

/**
 * Get current URL
 */
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];
    return $protocol . '://' . $host . $uri;
}

/**
 * Get base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . $path;
}

/**
 * Format currency (Indonesian Rupiah)
 */
function formatCurrency($amount) {
    return 'Rp ' . number_format($amount, 0, ',', '.');
}

/**
 * Format date to Indonesian format
 */
function formatDate($date, $format = 'd F Y') {
    $months = [
        1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
        5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
        9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    
    return $day . ' ' . $month . ' ' . $year;
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Validate email
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Generate random string
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * Upload image file
 */
function uploadImage($file, $target_dir = 'assets/images/uploads/') {
    $target_dir = rtrim($target_dir, '/') . '/';
    
    // Create directory if it doesn't exist
    if (!file_exists($target_dir)) {
        mkdir($target_dir, 0777, true);
    }
    
    $imageFileType = strtolower(pathinfo($file["name"], PATHINFO_EXTENSION));
    $new_filename = generateRandomString(20) . '.' . $imageFileType;
    $target_file = $target_dir . $new_filename;
    
    // Check if image file is a actual image or fake image
    $check = getimagesize($file["tmp_name"]);
    if($check === false) {
        return ['success' => false, 'message' => 'File bukan gambar yang valid.'];
    }
    
    // Check file size (max 5MB)
    if ($file["size"] > 5000000) {
        return ['success' => false, 'message' => 'Ukuran file terlalu besar. Maksimal 5MB.'];
    }
    
    // Allow certain file formats
    if($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg" && $imageFileType != "gif" ) {
        return ['success' => false, 'message' => 'Hanya file JPG, JPEG, PNG & GIF yang diizinkan.'];
    }
    
    // Upload file
    if (move_uploaded_file($file["tmp_name"], $target_file)) {
        return ['success' => true, 'filename' => $new_filename, 'path' => $target_file];
    } else {
        return ['success' => false, 'message' => 'Terjadi kesalahan saat mengupload file.'];
    }
}

/**
 * Get company information
 */
function getCompanyInfo($field_name = null) {
    global $pdo;

    // Default values
    $defaults = [
        'company_name' => 'Agen Air Fazza',
        'company_address' => 'Alamat Perusahaan',
        'company_phone' => '+62 812-3456-7890',
        'company_whatsapp' => '+62 812-3456-7890',
        'company_email' => '<EMAIL>',
        'business_hours' => 'Senin - Sabtu: 08:00 - 17:00',
        'company_vision' => 'Visi Perusahaan',
        'company_mission' => 'Misi Perusahaan',
        'company_history' => 'Sejarah Perusahaan'
    ];

    try {
        if ($field_name) {
            // Whitelist allowed field names for security
            $allowed_fields = array_keys($defaults);
            if (!in_array($field_name, $allowed_fields)) {
                return '';
            }

            // Get specific field from company_info table
            $stmt = $pdo->query("SELECT * FROM company_info LIMIT 1");
            $result = $stmt->fetch();
            return $result && isset($result[$field_name]) ? $result[$field_name] : $defaults[$field_name];
        } else {
            // Get all company info
            $stmt = $pdo->query("SELECT * FROM company_info LIMIT 1");
            $result = $stmt->fetch();
            return $result ? $result : $defaults;
        }
    } catch (PDOException $e) {
        // Return default values if table doesn't exist or has issues
        if ($field_name) {
            return isset($defaults[$field_name]) ? $defaults[$field_name] : '';
        } else {
            return $defaults;
        }
    }
}

/**
 * Get active products
 */
function getProducts($category = null, $featured_only = false, $limit = null) {
    global $pdo;
    
    $sql = "SELECT * FROM products WHERE status = 'active'";
    $params = [];
    
    if ($category) {
        $sql .= " AND category = ?";
        $params[] = $category;
    }
    
    if ($featured_only) {
        $sql .= " AND is_featured = 1";
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT ?";
        $params[] = $limit;
    }
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * Get active testimonials
 */
function getTestimonials($limit = null) {
    global $pdo;
    
    $sql = "SELECT * FROM testimonials WHERE status = 'active' ORDER BY created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$limit]);
    } else {
        $stmt = $pdo->query($sql);
    }
    
    return $stmt->fetchAll();
}

/**
 * Get gallery images
 */
function getGalleryImages($category = null, $limit = null) {
    global $pdo;
    
    $sql = "SELECT * FROM gallery WHERE status = 'active'";
    $params = [];
    
    if ($category) {
        $sql .= " AND category = ?";
        $params[] = $category;
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT ?";
        $params[] = $limit;
    }
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * Send contact message
 */
function sendContactMessage($name, $email, $message) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("INSERT INTO contact_messages (name, email, message) VALUES (?, ?, ?)");
        $stmt->execute([$name, $email, $message]);
        return ['success' => true, 'message' => 'Pesan berhasil dikirim. Terima kasih!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Terjadi kesalahan. Silakan coba lagi.'];
    }
}

/**
 * Check if user is admin (for admin panel)
 */
function isAdmin() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

/**
 * Redirect function
 */
function redirect($url) {
    header("Location: $url");
    exit();
}

/**
 * Get admin user info
 */
function getAdminInfo() {
    if (!isAdmin()) {
        return null;
    }

    return [
        'id' => $_SESSION['admin_id'] ?? null,
        'username' => $_SESSION['admin_username'] ?? null,
        'name' => $_SESSION['admin_name'] ?? null
    ];
}

?>
