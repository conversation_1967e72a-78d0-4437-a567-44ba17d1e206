    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        // Global admin panel functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.classList.remove('show');
                        setTimeout(() => {
                            if (alert.parentNode) {
                                alert.remove();
                            }
                        }, 150);
                    }
                }, 5000);
            });
            
            // Confirm delete actions
            const deleteButtons = document.querySelectorAll('[data-action="delete"]');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const itemName = this.getAttribute('data-item') || 'item ini';
                    if (confirm(`Apakah Anda yakin ingin menghapus ${itemName}? Tindakan ini tidak dapat dibatalkan.`)) {
                        window.location.href = this.href;
                    }
                });
            });
            
            // Auto-save form data to localStorage
            const forms = document.querySelectorAll('form[data-autosave]');
            forms.forEach(form => {
                const formId = form.getAttribute('data-autosave');
                
                // Load saved data
                const savedData = localStorage.getItem(`form_${formId}`);
                if (savedData) {
                    try {
                        const data = JSON.parse(savedData);
                        Object.keys(data).forEach(key => {
                            const field = form.querySelector(`[name="${key}"]`);
                            if (field) {
                                field.value = data[key];
                            }
                        });
                    } catch (e) {
                        console.error('Error loading saved form data:', e);
                    }
                }
                
                // Save data on input
                form.addEventListener('input', function() {
                    const formData = new FormData(form);
                    const data = {};
                    for (let [key, value] of formData.entries()) {
                        data[key] = value;
                    }
                    localStorage.setItem(`form_${formId}`, JSON.stringify(data));
                });
                
                // Clear saved data on successful submit
                form.addEventListener('submit', function() {
                    setTimeout(() => {
                        localStorage.removeItem(`form_${formId}`);
                    }, 1000);
                });
            });
            
            // Mobile sidebar toggle
            const sidebarToggle = document.querySelector('.navbar-toggler');
            const sidebar = document.querySelector('.sidebar');
            
            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
                
                // Close sidebar when clicking outside on mobile
                document.addEventListener('click', function(e) {
                    if (window.innerWidth < 768) {
                        if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                            sidebar.classList.remove('show');
                        }
                    }
                });
            }
            
            // Search functionality
            const searchInputs = document.querySelectorAll('[data-search]');
            searchInputs.forEach(input => {
                const targetSelector = input.getAttribute('data-search');
                const targets = document.querySelectorAll(targetSelector);
                
                input.addEventListener('input', function() {
                    const query = this.value.toLowerCase();
                    
                    targets.forEach(target => {
                        const text = target.textContent.toLowerCase();
                        if (text.includes(query)) {
                            target.style.display = '';
                        } else {
                            target.style.display = 'none';
                        }
                    });
                });
            });
            
            // Image preview functionality
            const imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
            imageInputs.forEach(input => {
                input.addEventListener('change', function() {
                    const file = this.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            let preview = input.parentElement.querySelector('.image-preview');
                            if (!preview) {
                                preview = document.createElement('img');
                                preview.className = 'image-preview mt-2';
                                preview.style.cssText = 'max-width: 200px; max-height: 200px; object-fit: cover; border-radius: 0.375rem;';
                                input.parentElement.appendChild(preview);
                            }
                            preview.src = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            });
            
            // Auto-resize textareas
            const textareas = document.querySelectorAll('textarea[data-autoresize]');
            textareas.forEach(textarea => {
                function resize() {
                    textarea.style.height = 'auto';
                    textarea.style.height = textarea.scrollHeight + 'px';
                }
                
                textarea.addEventListener('input', resize);
                resize(); // Initial resize
            });
            
            // Copy to clipboard functionality
            const copyButtons = document.querySelectorAll('[data-copy]');
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const text = this.getAttribute('data-copy');
                    navigator.clipboard.writeText(text).then(() => {
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-check me-1"></i>Disalin!';
                        this.classList.add('btn-success');
                        
                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.classList.remove('btn-success');
                        }, 2000);
                    });
                });
            });
        });
        
        // Global functions
        function showLoading(element) {
            if (element) {
                element.classList.add('loading');
            }
        }
        
        function hideLoading(element) {
            if (element) {
                element.classList.remove('loading');
            }
        }
        
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer') || createAlertContainer();
            
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            alertContainer.appendChild(alert);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
        
        function createAlertContainer() {
            const container = document.createElement('div');
            container.id = 'alertContainer';
            container.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
            return container;
        }
        
        // AJAX form submission
        function submitForm(form, callback) {
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Memproses...';
            submitBtn.disabled = true;
            
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (callback) {
                    callback(data);
                } else {
                    if (data.success) {
                        showAlert(data.message, 'success');
                        if (data.redirect) {
                            setTimeout(() => {
                                window.location.href = data.redirect;
                            }, 1500);
                        }
                    } else {
                        showAlert(data.message, 'danger');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Terjadi kesalahan. Silakan coba lagi.', 'danger');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        }
        
        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('id-ID', {
                style: 'currency',
                currency: 'IDR',
                minimumFractionDigits: 0
            }).format(amount);
        }
        
        // Format date
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('id-ID', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        // Session timeout warning
        let sessionTimeout;
        function resetSessionTimeout() {
            clearTimeout(sessionTimeout);
            sessionTimeout = setTimeout(() => {
                if (confirm('Sesi Anda akan berakhir dalam 5 menit. Klik OK untuk memperpanjang sesi.')) {
                    fetch('refresh_session.php')
                        .then(() => resetSessionTimeout())
                        .catch(() => {
                            alert('Sesi telah berakhir. Anda akan diarahkan ke halaman login.');
                            window.location.href = 'index.php';
                        });
                } else {
                    window.location.href = 'logout.php';
                }
            }, 25 * 60 * 1000); // 25 minutes
        }
        
        // Initialize session timeout
        resetSessionTimeout();
        
        // Reset timeout on user activity
        ['click', 'keypress', 'scroll', 'mousemove'].forEach(event => {
            document.addEventListener(event, resetSessionTimeout);
        });
    </script>
    
    <!-- Page-specific scripts -->
    <?php if (isset($page_scripts)): ?>
        <?php echo $page_scripts; ?>
    <?php endif; ?>
</body>
</html>
