<?php
/**
 * Database Installation Script for Agen Air Fazza
 * Run this file to set up the database automatically
 */

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$message = '';
$error = '';

// Step 2: Process installation
if ($step === 2 && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'sim_airfazza';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    
    try {
        // Test connection
        $dsn_temp = "mysql:host=$db_host;charset=utf8mb4";
        $pdo_temp = new PDO($dsn_temp, $db_user, $db_pass);
        $pdo_temp->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo_temp->exec("CREATE DATABASE IF NOT EXISTS $db_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // Connect to the database
        $dsn = "mysql:host=$db_host;dbname=$db_name;charset=utf8mb4";
        $pdo = new PDO($dsn, $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Update config file
        $config_content = "<?php
/**
 * Database Configuration for Agen Air Fazza
 */

define('DB_HOST', '$db_host');
define('DB_NAME', '$db_name');
define('DB_USER', '$db_user');
define('DB_PASS', '$db_pass');
define('DB_CHARSET', 'utf8mb4');

// Create database connection
try {
    \$dsn = \"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=\" . DB_CHARSET;
    \$pdo = new PDO(\$dsn, DB_USER, DB_PASS);
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    \$pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
} catch(PDOException \$e) {
    die(\"Database connection failed: \" . \$e->getMessage());
}
?>";
        
        file_put_contents('config/database.php', $config_content);
        
        // Run table setup
        include_once 'config/setup-tables.php';
        setupDatabase($pdo);
        
        $step = 3;
        $message = 'Database installed successfully!';
        
    } catch (Exception $e) {
        $error = 'Installation failed: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Database - Agen Air Fazza</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 2rem auto;
            max-width: 600px;
            overflow: hidden;
        }
        .install-header {
            background: linear-gradient(135deg, #2563eb, #06b6d4);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .install-content {
            padding: 2rem;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 1rem;
            font-weight: bold;
        }
        .step.active {
            background: #2563eb;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step.pending {
            background: #e9ecef;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="install-header">
                <h1><i class="fas fa-database me-2"></i>Database Installation</h1>
                <p class="mb-0">Setup database for Agen Air Fazza</p>
            </div>
            
            <div class="install-content">
                <!-- Step Indicator -->
                <div class="step-indicator">
                    <div class="step <?php echo $step >= 1 ? 'active' : 'pending'; ?>">1</div>
                    <div class="step <?php echo $step >= 2 ? 'active' : 'pending'; ?>">2</div>
                    <div class="step <?php echo $step >= 3 ? 'completed' : 'pending'; ?>">3</div>
                </div>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($step === 1): ?>
                    <!-- Step 1: Welcome -->
                    <div class="text-center">
                        <h3>Welcome to Agen Air Fazza</h3>
                        <p class="text-muted">This installer will set up your database automatically.</p>
                        
                        <div class="alert alert-info text-start">
                            <h6><i class="fas fa-info-circle me-2"></i>Requirements:</h6>
                            <ul class="mb-0">
                                <li>MySQL or MariaDB server</li>
                                <li>PHP 7.4 or higher</li>
                                <li>PDO MySQL extension</li>
                                <li>Write permissions for config folder</li>
                            </ul>
                        </div>
                        
                        <a href="?step=2" class="btn btn-primary btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>
                            Start Installation
                        </a>
                    </div>
                
                <?php elseif ($step === 2): ?>
                    <!-- Step 2: Database Configuration -->
                    <h3>Database Configuration</h3>
                    <p class="text-muted">Enter your database connection details:</p>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label for="db_host" class="form-label">Database Host</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" 
                                   value="localhost" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_name" class="form-label">Database Name</label>
                            <input type="text" class="form-control" id="db_name" name="db_name" 
                                   value="sim_airfazza" required>
                            <div class="form-text">Database will be created if it doesn't exist</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_user" class="form-label">Database Username</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" 
                                   value="root" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="db_pass" class="form-label">Database Password</label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass">
                            <div class="form-text">Leave empty if no password</div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-cog me-2"></i>
                                Install Database
                            </button>
                        </div>
                    </form>
                
                <?php elseif ($step === 3): ?>
                    <!-- Step 3: Success -->
                    <div class="text-center">
                        <div class="text-success mb-4">
                            <i class="fas fa-check-circle" style="font-size: 4rem;"></i>
                        </div>
                        
                        <h3>Installation Complete!</h3>
                        <p class="text-muted">Your database has been set up successfully.</p>
                        
                        <div class="alert alert-info text-start">
                            <h6><i class="fas fa-key me-2"></i>Default Admin Login:</h6>
                            <p class="mb-1"><strong>Username:</strong> admin</p>
                            <p class="mb-0"><strong>Password:</strong> admin123</p>
                        </div>
                        
                        <div class="alert alert-warning text-start">
                            <h6><i class="fas fa-shield-alt me-2"></i>Security Note:</h6>
                            <p class="mb-0">Please delete this install.php file after installation for security.</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="index.php" class="btn btn-success btn-lg">
                                <i class="fas fa-home me-2"></i>
                                Visit Website
                            </a>
                            <a href="admin/index.php" class="btn btn-primary">
                                <i class="fas fa-user-shield me-2"></i>
                                Admin Login
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
