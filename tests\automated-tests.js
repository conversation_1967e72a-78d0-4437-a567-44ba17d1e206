/**
 * Automated Testing Suite for Agen Air Fazza
 * Comprehensive testing of all website features and functionality
 */

class TestSuite {
    constructor() {
        this.tests = [];
        this.results = {
            passed: 0,
            failed: 0,
            total: 0,
            details: []
        };
        this.startTime = Date.now();
    }

    // Add a test to the suite
    addTest(name, testFunction, category = 'General') {
        this.tests.push({
            name,
            testFunction,
            category,
            status: 'pending'
        });
    }

    // Run all tests
    async runAllTests() {
        console.log('🚀 Starting Automated Test Suite for Agen Air Fazza');
        console.log('=' .repeat(60));

        for (const test of this.tests) {
            await this.runTest(test);
        }

        this.generateReport();
        return this.results;
    }

    // Run individual test
    async runTest(test) {
        try {
            console.log(`🧪 Running: ${test.name}`);
            const result = await test.testFunction();
            
            if (result.success) {
                test.status = 'passed';
                this.results.passed++;
                console.log(`✅ PASSED: ${test.name}`);
            } else {
                test.status = 'failed';
                this.results.failed++;
                console.log(`❌ FAILED: ${test.name} - ${result.message}`);
            }

            this.results.details.push({
                name: test.name,
                category: test.category,
                status: test.status,
                message: result.message || 'Test completed successfully',
                duration: result.duration || 0
            });

        } catch (error) {
            test.status = 'error';
            this.results.failed++;
            console.log(`💥 ERROR: ${test.name} - ${error.message}`);
            
            this.results.details.push({
                name: test.name,
                category: test.category,
                status: 'error',
                message: error.message,
                duration: 0
            });
        }

        this.results.total++;
    }

    // Generate test report
    generateReport() {
        const endTime = Date.now();
        const duration = (endTime - this.startTime) / 1000;

        console.log('\n' + '=' .repeat(60));
        console.log('📊 TEST RESULTS SUMMARY');
        console.log('=' .repeat(60));
        console.log(`Total Tests: ${this.results.total}`);
        console.log(`Passed: ${this.results.passed} ✅`);
        console.log(`Failed: ${this.results.failed} ❌`);
        console.log(`Success Rate: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);
        console.log(`Duration: ${duration.toFixed(2)} seconds`);
        console.log('=' .repeat(60));

        // Group results by category
        const categories = {};
        this.results.details.forEach(test => {
            if (!categories[test.category]) {
                categories[test.category] = { passed: 0, failed: 0, total: 0 };
            }
            categories[test.category].total++;
            if (test.status === 'passed') {
                categories[test.category].passed++;
            } else {
                categories[test.category].failed++;
            }
        });

        console.log('\n📋 RESULTS BY CATEGORY:');
        Object.keys(categories).forEach(category => {
            const cat = categories[category];
            const rate = ((cat.passed / cat.total) * 100).toFixed(1);
            console.log(`${category}: ${cat.passed}/${cat.total} (${rate}%)`);
        });

        // Show failed tests
        const failedTests = this.results.details.filter(test => test.status !== 'passed');
        if (failedTests.length > 0) {
            console.log('\n❌ FAILED TESTS:');
            failedTests.forEach(test => {
                console.log(`- ${test.name}: ${test.message}`);
            });
        }
    }
}

// Test Functions
const Tests = {
    // Page Loading Tests
    async testPageLoading() {
        const startTime = Date.now();
        try {
            // Test if main elements exist
            const requiredElements = [
                'header', 'nav', 'main', 'footer',
                '.navbar', '.hero-section'
            ];

            for (const selector of requiredElements) {
                const element = document.querySelector(selector);
                if (!element) {
                    return {
                        success: false,
                        message: `Required element not found: ${selector}`,
                        duration: Date.now() - startTime
                    };
                }
            }

            return {
                success: true,
                message: 'All required page elements loaded successfully',
                duration: Date.now() - startTime
            };
        } catch (error) {
            return {
                success: false,
                message: `Page loading error: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    },

    // Navigation Tests
    async testNavigation() {
        const startTime = Date.now();
        try {
            const navLinks = document.querySelectorAll('.nav-link');
            if (navLinks.length === 0) {
                return {
                    success: false,
                    message: 'No navigation links found',
                    duration: Date.now() - startTime
                };
            }

            // Test if all nav links have proper href attributes
            for (const link of navLinks) {
                const href = link.getAttribute('href');
                if (!href || href === '#') {
                    return {
                        success: false,
                        message: `Navigation link missing proper href: ${link.textContent}`,
                        duration: Date.now() - startTime
                    };
                }
            }

            // Test mobile menu toggle
            const mobileToggle = document.querySelector('.navbar-toggler');
            const mobileMenu = document.querySelector('.navbar-collapse');
            
            if (mobileToggle && mobileMenu) {
                // Simulate mobile menu click
                mobileToggle.click();
                await new Promise(resolve => setTimeout(resolve, 100));
                
                if (!mobileMenu.classList.contains('show')) {
                    return {
                        success: false,
                        message: 'Mobile menu toggle not working',
                        duration: Date.now() - startTime
                    };
                }
                
                // Close mobile menu
                mobileToggle.click();
            }

            return {
                success: true,
                message: `Navigation test passed - ${navLinks.length} links tested`,
                duration: Date.now() - startTime
            };
        } catch (error) {
            return {
                success: false,
                message: `Navigation test error: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    },

    // Form Validation Tests
    async testFormValidation() {
        const startTime = Date.now();
        try {
            const forms = document.querySelectorAll('form');
            if (forms.length === 0) {
                return {
                    success: true,
                    message: 'No forms found on current page',
                    duration: Date.now() - startTime
                };
            }

            for (const form of forms) {
                // Test required fields
                const requiredFields = form.querySelectorAll('[required]');
                for (const field of requiredFields) {
                    // Test empty field validation
                    field.value = '';
                    if (field.checkValidity()) {
                        return {
                            success: false,
                            message: `Required field validation failed: ${field.name || field.id}`,
                            duration: Date.now() - startTime
                        };
                    }
                }

                // Test email validation
                const emailFields = form.querySelectorAll('input[type="email"]');
                for (const emailField of emailFields) {
                    emailField.value = 'invalid-email';
                    if (emailField.checkValidity()) {
                        return {
                            success: false,
                            message: 'Email validation failed',
                            duration: Date.now() - startTime
                        };
                    }
                    
                    emailField.value = '<EMAIL>';
                    if (!emailField.checkValidity()) {
                        return {
                            success: false,
                            message: 'Valid email rejected',
                            duration: Date.now() - startTime
                        };
                    }
                }
            }

            return {
                success: true,
                message: `Form validation test passed - ${forms.length} forms tested`,
                duration: Date.now() - startTime
            };
        } catch (error) {
            return {
                success: false,
                message: `Form validation test error: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    },

    // Responsive Design Tests
    async testResponsiveDesign() {
        const startTime = Date.now();
        try {
            const originalWidth = window.innerWidth;
            const testWidths = [320, 768, 1024, 1920]; // Mobile, tablet, desktop, large desktop
            
            for (const width of testWidths) {
                // Simulate different screen sizes
                Object.defineProperty(window, 'innerWidth', {
                    writable: true,
                    configurable: true,
                    value: width
                });
                
                // Trigger resize event
                window.dispatchEvent(new Event('resize'));
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // Check if mobile menu is properly hidden/shown
                const mobileMenu = document.querySelector('.navbar-collapse');
                if (mobileMenu) {
                    const isVisible = window.getComputedStyle(mobileMenu).display !== 'none';
                    // Mobile menu should be collapsible on small screens
                    if (width < 768 && !mobileMenu.classList.contains('collapse')) {
                        return {
                            success: false,
                            message: `Mobile menu not collapsible at ${width}px`,
                            duration: Date.now() - startTime
                        };
                    }
                }
            }
            
            // Restore original width
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: originalWidth
            });
            window.dispatchEvent(new Event('resize'));

            return {
                success: true,
                message: 'Responsive design test passed',
                duration: Date.now() - startTime
            };
        } catch (error) {
            return {
                success: false,
                message: `Responsive design test error: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    },

    // Accessibility Tests
    async testAccessibility() {
        const startTime = Date.now();
        try {
            const issues = [];

            // Test for alt attributes on images
            const images = document.querySelectorAll('img');
            images.forEach((img, index) => {
                if (!img.hasAttribute('alt')) {
                    issues.push(`Image ${index + 1} missing alt attribute`);
                }
            });

            // Test for form labels
            const inputs = document.querySelectorAll('input, textarea, select');
            inputs.forEach((input, index) => {
                const id = input.id;
                const label = document.querySelector(`label[for="${id}"]`);
                if (!label && !input.hasAttribute('aria-label') && !input.hasAttribute('aria-labelledby')) {
                    issues.push(`Input ${index + 1} missing label or aria-label`);
                }
            });

            // Test for heading hierarchy
            const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
            let previousLevel = 0;
            headings.forEach((heading, index) => {
                const level = parseInt(heading.tagName.charAt(1));
                if (index === 0 && level !== 1) {
                    issues.push('Page should start with h1');
                }
                if (level > previousLevel + 1) {
                    issues.push(`Heading level skipped: ${heading.tagName} after h${previousLevel}`);
                }
                previousLevel = level;
            });

            // Test for focus indicators
            const focusableElements = document.querySelectorAll('a, button, input, textarea, select');
            // This is a basic test - in real scenarios, you'd check computed styles
            
            if (issues.length > 0) {
                return {
                    success: false,
                    message: `Accessibility issues found: ${issues.join(', ')}`,
                    duration: Date.now() - startTime
                };
            }

            return {
                success: true,
                message: 'Accessibility test passed',
                duration: Date.now() - startTime
            };
        } catch (error) {
            return {
                success: false,
                message: `Accessibility test error: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    },

    // Performance Tests
    async testPerformance() {
        const startTime = Date.now();
        try {
            const performanceData = {
                loadTime: 0,
                domElements: document.querySelectorAll('*').length,
                images: document.querySelectorAll('img').length,
                scripts: document.querySelectorAll('script').length,
                stylesheets: document.querySelectorAll('link[rel="stylesheet"]').length
            };

            // Check if page loaded within reasonable time
            if (performance.timing) {
                performanceData.loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            }

            // Performance thresholds
            const thresholds = {
                maxLoadTime: 5000, // 5 seconds
                maxDomElements: 1500,
                maxImages: 50,
                maxScripts: 10,
                maxStylesheets: 10
            };

            const issues = [];
            if (performanceData.loadTime > thresholds.maxLoadTime) {
                issues.push(`Load time too high: ${performanceData.loadTime}ms`);
            }
            if (performanceData.domElements > thresholds.maxDomElements) {
                issues.push(`Too many DOM elements: ${performanceData.domElements}`);
            }
            if (performanceData.images > thresholds.maxImages) {
                issues.push(`Too many images: ${performanceData.images}`);
            }

            if (issues.length > 0) {
                return {
                    success: false,
                    message: `Performance issues: ${issues.join(', ')}`,
                    duration: Date.now() - startTime
                };
            }

            return {
                success: true,
                message: `Performance test passed - Load time: ${performanceData.loadTime}ms`,
                duration: Date.now() - startTime
            };
        } catch (error) {
            return {
                success: false,
                message: `Performance test error: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    },

    // SEO Tests
    async testSEO() {
        const startTime = Date.now();
        try {
            const issues = [];

            // Check for title tag
            const title = document.querySelector('title');
            if (!title || title.textContent.length < 10) {
                issues.push('Missing or too short title tag');
            }

            // Check for meta description
            const metaDescription = document.querySelector('meta[name="description"]');
            if (!metaDescription || metaDescription.content.length < 50) {
                issues.push('Missing or too short meta description');
            }

            // Check for h1 tag
            const h1 = document.querySelector('h1');
            if (!h1) {
                issues.push('Missing h1 tag');
            }

            // Check for canonical URL
            const canonical = document.querySelector('link[rel="canonical"]');
            // This is optional, so we won't fail the test

            // Check for Open Graph tags
            const ogTitle = document.querySelector('meta[property="og:title"]');
            const ogDescription = document.querySelector('meta[property="og:description"]');
            if (!ogTitle || !ogDescription) {
                issues.push('Missing Open Graph tags');
            }

            if (issues.length > 0) {
                return {
                    success: false,
                    message: `SEO issues found: ${issues.join(', ')}`,
                    duration: Date.now() - startTime
                };
            }

            return {
                success: true,
                message: 'SEO test passed',
                duration: Date.now() - startTime
            };
        } catch (error) {
            return {
                success: false,
                message: `SEO test error: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    },

    // Mobile Optimization Tests
    async testMobileOptimization() {
        const startTime = Date.now();
        try {
            const issues = [];

            // Check viewport meta tag
            const viewport = document.querySelector('meta[name="viewport"]');
            if (!viewport) {
                issues.push('Missing viewport meta tag');
            } else if (!viewport.content.includes('width=device-width')) {
                issues.push('Viewport meta tag missing width=device-width');
            }

            // Check for touch-friendly elements
            const buttons = document.querySelectorAll('button, .btn, a');
            let smallButtons = 0;
            buttons.forEach(button => {
                const rect = button.getBoundingClientRect();
                if (rect.width < 44 || rect.height < 44) {
                    smallButtons++;
                }
            });

            if (smallButtons > buttons.length * 0.1) {
                issues.push(`${smallButtons} buttons are smaller than 44px (touch-friendly size)`);
            }

            // Check for mobile-specific CSS
            const mobileCSS = document.querySelector('link[href*="mobile"]') ||
                             document.querySelector('style').textContent.includes('@media');
            if (!mobileCSS) {
                issues.push('No mobile-specific CSS detected');
            }

            if (issues.length > 0) {
                return {
                    success: false,
                    message: `Mobile optimization issues: ${issues.join(', ')}`,
                    duration: Date.now() - startTime
                };
            }

            return {
                success: true,
                message: 'Mobile optimization test passed',
                duration: Date.now() - startTime
            };
        } catch (error) {
            return {
                success: false,
                message: `Mobile optimization test error: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    },

    // PWA Tests
    async testPWA() {
        const startTime = Date.now();
        try {
            const features = {
                serviceWorker: 'serviceWorker' in navigator,
                manifest: !!document.querySelector('link[rel="manifest"]'),
                https: location.protocol === 'https:' || location.hostname === 'localhost',
                responsive: !!document.querySelector('meta[name="viewport"]')
            };

            const score = Object.values(features).filter(Boolean).length;
            const total = Object.keys(features).length;

            return {
                success: score >= total * 0.75, // 75% of features required
                message: `PWA features: ${score}/${total} - ${Object.entries(features).map(([key, value]) => `${key}: ${value ? '✓' : '✗'}`).join(', ')}`,
                duration: Date.now() - startTime
            };
        } catch (error) {
            return {
                success: false,
                message: `PWA test error: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    },

    // Security Tests
    async testSecurity() {
        const startTime = Date.now();
        try {
            const issues = [];

            // Check HTTPS
            if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
                issues.push('Site not served over HTTPS');
            }

            // Check for mixed content
            const insecureResources = Array.from(document.querySelectorAll('img, script, link'))
                .filter(el => {
                    const src = el.src || el.href;
                    return src && src.startsWith('http://') && location.protocol === 'https:';
                });

            if (insecureResources.length > 0) {
                issues.push(`${insecureResources.length} insecure resources found`);
            }

            // Check for external links security
            const externalLinks = Array.from(document.querySelectorAll('a[href^="http"]'))
                .filter(link => !link.href.includes(location.hostname));

            const unsafeExternalLinks = externalLinks.filter(link =>
                !link.hasAttribute('rel') ||
                (!link.rel.includes('noopener') && !link.rel.includes('noreferrer'))
            );

            if (unsafeExternalLinks.length > 0) {
                issues.push(`${unsafeExternalLinks.length} external links missing security attributes`);
            }

            if (issues.length > 0) {
                return {
                    success: false,
                    message: `Security issues: ${issues.join(', ')}`,
                    duration: Date.now() - startTime
                };
            }

            return {
                success: true,
                message: 'Security test passed',
                duration: Date.now() - startTime
            };
        } catch (error) {
            return {
                success: false,
                message: `Security test error: ${error.message}`,
                duration: Date.now() - startTime
            };
        }
    }
};

// Initialize and run tests
function initializeTestSuite() {
    const testSuite = new TestSuite();

    // Add all tests
    testSuite.addTest('Page Loading', Tests.testPageLoading, 'Core Functionality');
    testSuite.addTest('Navigation', Tests.testNavigation, 'Core Functionality');
    testSuite.addTest('Form Validation', Tests.testFormValidation, 'Forms');
    testSuite.addTest('Responsive Design', Tests.testResponsiveDesign, 'Responsive');
    testSuite.addTest('Accessibility', Tests.testAccessibility, 'Accessibility');
    testSuite.addTest('Performance', Tests.testPerformance, 'Performance');
    testSuite.addTest('SEO', Tests.testSEO, 'SEO');
    testSuite.addTest('Mobile Optimization', Tests.testMobileOptimization, 'Mobile');
    testSuite.addTest('PWA Features', Tests.testPWA, 'PWA');
    testSuite.addTest('Security', Tests.testSecurity, 'Security');

    return testSuite;
}

// Export for use in browser
if (typeof window !== 'undefined') {
    window.TestSuite = TestSuite;
    window.Tests = Tests;
    window.initializeTestSuite = initializeTestSuite;
}

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TestSuite, Tests, initializeTestSuite };
}
