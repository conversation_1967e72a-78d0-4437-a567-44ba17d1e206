<?php
/**
 * Database Check Script
 * Use this to verify your database setup is working correctly
 */

echo "<h2>Database Connection Check</h2>";

try {
    // Include database connection
    require_once 'config/database.php';
    echo "✅ Database connection successful!<br>";
    
    // Check if tables exist
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<h3>Tables in database:</h3>";
    
    if (empty($tables)) {
        echo "❌ No tables found. Running setup...<br>";
        include_once 'config/setup-tables.php';
        setupDatabase($pdo);
        echo "✅ Tables created successfully!<br>";
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    }
    
    foreach ($tables as $table) {
        echo "✅ Table: $table<br>";
    }
    
    // Test company_info table
    echo "<h3>Testing company_info table:</h3>";
    $stmt = $pdo->query("SELECT * FROM company_info LIMIT 1");
    $company_info = $stmt->fetch();
    
    if ($company_info) {
        echo "✅ Company info found:<br>";
        echo "- Company Name: " . htmlspecialchars($company_info['company_name']) . "<br>";
        echo "- Phone: " . htmlspecialchars($company_info['company_phone']) . "<br>";
        echo "- Email: " . htmlspecialchars($company_info['company_email']) . "<br>";
    } else {
        echo "❌ No company info found<br>";
    }
    
    // Test functions
    echo "<h3>Testing functions:</h3>";
    require_once 'includes/functions.php';
    
    $company_name = getCompanyInfo('company_name');
    echo "✅ getCompanyInfo('company_name'): " . htmlspecialchars($company_name) . "<br>";
    
    $all_info = getCompanyInfo();
    echo "✅ getCompanyInfo() returned " . count($all_info) . " fields<br>";
    
    echo "<h3>All tests passed! ✅</h3>";
    echo "<p><a href='index.php'>Go to Website</a> | <a href='admin/index.php'>Go to Admin</a></p>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "<h3>Troubleshooting:</h3>";
    echo "<ol>";
    echo "<li>Make sure MySQL/MariaDB is running</li>";
    echo "<li>Check database credentials in config/database.php</li>";
    echo "<li>Run the installer: <a href='install.php'>install.php</a></li>";
    echo "</ol>";
}
?>
