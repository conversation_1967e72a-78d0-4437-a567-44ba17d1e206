/**
 * Test Report Generator for Agen Air Fazza
 * Generates comprehensive testing reports in multiple formats
 */

class TestReportGenerator {
    constructor() {
        this.reportData = {
            metadata: {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                screenResolution: `${screen.width}x${screen.height}`,
                viewportSize: `${window.innerWidth}x${window.innerHeight}`,
                testDuration: 0
            },
            summary: {
                totalTests: 0,
                passed: 0,
                failed: 0,
                successRate: 0,
                categories: {}
            },
            testResults: [],
            performanceMetrics: null,
            recommendations: [],
            issues: []
        };
    }

    // Set test results from automated tests
    setTestResults(results) {
        this.reportData.summary = {
            totalTests: results.total,
            passed: results.passed,
            failed: results.failed,
            successRate: ((results.passed / results.total) * 100).toFixed(1)
        };

        this.reportData.testResults = results.details;

        // Group by categories
        const categories = {};
        results.details.forEach(test => {
            if (!categories[test.category]) {
                categories[test.category] = { passed: 0, failed: 0, total: 0 };
            }
            categories[test.category].total++;
            if (test.status === 'passed') {
                categories[test.category].passed++;
            } else {
                categories[test.category].failed++;
            }
        });
        this.reportData.summary.categories = categories;

        // Extract issues and recommendations
        this.extractIssuesAndRecommendations(results.details);
    }

    // Set performance metrics
    setPerformanceMetrics(performanceReport) {
        this.reportData.performanceMetrics = performanceReport;
        
        // Add performance recommendations
        if (performanceReport.recommendations) {
            this.reportData.recommendations.push(...performanceReport.recommendations);
        }
    }

    // Extract issues and recommendations from test results
    extractIssuesAndRecommendations(testDetails) {
        testDetails.forEach(test => {
            if (test.status !== 'passed') {
                this.reportData.issues.push({
                    category: test.category,
                    test: test.name,
                    severity: this.determineSeverity(test.category, test.name),
                    description: test.message,
                    recommendations: this.getTestRecommendations(test.category, test.name)
                });
            }
        });
    }

    // Determine issue severity
    determineSeverity(category, testName) {
        const highSeverityTests = [
            'Page Loading', 'Navigation', 'Form Validation', 'Security'
        ];
        const mediumSeverityTests = [
            'Performance', 'Accessibility', 'SEO'
        ];

        if (highSeverityTests.includes(testName)) {
            return 'high';
        } else if (mediumSeverityTests.includes(testName)) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    // Get recommendations for specific test failures
    getTestRecommendations(category, testName) {
        const recommendations = {
            'Page Loading': [
                'Check for JavaScript errors in console',
                'Verify all required CSS and JS files are loading',
                'Ensure proper HTML structure',
                'Test on different browsers and devices'
            ],
            'Navigation': [
                'Verify all navigation links have proper href attributes',
                'Test mobile menu functionality',
                'Ensure navigation is accessible via keyboard',
                'Check for broken internal links'
            ],
            'Form Validation': [
                'Implement proper client-side validation',
                'Add server-side validation for security',
                'Provide clear error messages',
                'Test with various input combinations'
            ],
            'Responsive Design': [
                'Test on multiple device sizes',
                'Use CSS media queries effectively',
                'Ensure touch targets are at least 44px',
                'Optimize images for different screen densities'
            ],
            'Accessibility': [
                'Add alt text to all images',
                'Ensure proper heading hierarchy',
                'Associate form labels with inputs',
                'Test with screen readers',
                'Improve color contrast ratios'
            ],
            'Performance': [
                'Optimize images and use modern formats',
                'Minimize and compress CSS/JS files',
                'Implement lazy loading',
                'Use a Content Delivery Network (CDN)',
                'Optimize server response times'
            ],
            'SEO': [
                'Add unique, descriptive title tags',
                'Write compelling meta descriptions',
                'Implement proper heading structure',
                'Add Open Graph and Twitter Card tags',
                'Create XML sitemap'
            ],
            'Mobile Optimization': [
                'Add viewport meta tag',
                'Ensure touch-friendly button sizes',
                'Optimize for mobile networks',
                'Test on real mobile devices',
                'Implement mobile-specific features'
            ],
            'PWA': [
                'Add web app manifest',
                'Implement service worker',
                'Ensure HTTPS deployment',
                'Add app icons for different sizes',
                'Test offline functionality'
            ],
            'Security': [
                'Deploy with HTTPS',
                'Fix mixed content issues',
                'Add security headers',
                'Validate and sanitize user inputs',
                'Use secure external link attributes'
            ]
        };

        return recommendations[testName] || ['Review test failure details and implement appropriate fixes'];
    }

    // Generate HTML report
    generateHTMLReport() {
        const html = `
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Report - Agen Air Fazza</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #2563eb, #06b6d4); color: white; padding: 2rem; border-radius: 8px 8px 0 0; }
        .content { padding: 2rem; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .metric { background: #f8f9fa; padding: 1.5rem; border-radius: 8px; text-align: center; }
        .metric-value { font-size: 2rem; font-weight: bold; margin-bottom: 0.5rem; }
        .metric-label { color: #6c757d; font-size: 0.875rem; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
        .section { margin-bottom: 2rem; }
        .section h2 { border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }
        .test-result { display: flex; justify-content: space-between; align-items: center; padding: 0.75rem; border-bottom: 1px solid #e9ecef; }
        .test-status { width: 12px; height: 12px; border-radius: 50%; margin-right: 0.5rem; }
        .status-passed { background: #28a745; }
        .status-failed { background: #dc3545; }
        .status-error { background: #fd7e14; }
        .issue { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 1rem; margin-bottom: 1rem; }
        .issue.high { background: #f8d7da; border-color: #f5c6cb; }
        .issue.medium { background: #fff3cd; border-color: #ffeaa7; }
        .issue.low { background: #d1ecf1; border-color: #bee5eb; }
        .recommendations { background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 1rem; margin-top: 1rem; }
        .recommendations ul { margin: 0.5rem 0; padding-left: 1.5rem; }
        .performance-metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; }
        .performance-metric { background: #f8f9fa; padding: 1rem; border-radius: 4px; text-align: center; }
        @media print { body { background: white; } .container { box-shadow: none; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Report</h1>
            <p>Comprehensive Quality Assurance Report for Agen Air Fazza</p>
            <p><strong>Generated:</strong> ${new Date(this.reportData.metadata.timestamp).toLocaleString('id-ID')}</p>
            <p><strong>URL:</strong> ${this.reportData.metadata.url}</p>
        </div>
        
        <div class="content">
            <!-- Summary -->
            <div class="section">
                <h2>📊 Test Summary</h2>
                <div class="summary">
                    <div class="metric">
                        <div class="metric-value">${this.reportData.summary.totalTests}</div>
                        <div class="metric-label">Total Tests</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value success">${this.reportData.summary.passed}</div>
                        <div class="metric-label">Passed</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value danger">${this.reportData.summary.failed}</div>
                        <div class="metric-label">Failed</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value ${this.reportData.summary.successRate >= 90 ? 'success' : this.reportData.summary.successRate >= 70 ? 'warning' : 'danger'}">${this.reportData.summary.successRate}%</div>
                        <div class="metric-label">Success Rate</div>
                    </div>
                </div>
            </div>

            <!-- Test Results by Category -->
            <div class="section">
                <h2>📋 Test Results by Category</h2>
                ${Object.entries(this.reportData.summary.categories).map(([category, stats]) => `
                    <div class="test-result">
                        <div>
                            <strong>${category}</strong>
                            <span class="text-muted">(${stats.passed}/${stats.total} passed)</span>
                        </div>
                        <div class="progress-bar" style="width: 100px; height: 8px; background: #e9ecef; border-radius: 4px;">
                            <div style="width: ${(stats.passed / stats.total) * 100}%; height: 100%; background: ${stats.passed === stats.total ? '#28a745' : '#ffc107'}; border-radius: 4px;"></div>
                        </div>
                    </div>
                `).join('')}
            </div>

            <!-- Detailed Test Results -->
            <div class="section">
                <h2>🔍 Detailed Test Results</h2>
                ${this.reportData.testResults.map(test => `
                    <div class="test-result">
                        <div style="display: flex; align-items: center;">
                            <span class="test-status status-${test.status}"></span>
                            <div>
                                <strong>${test.name}</strong>
                                <div style="font-size: 0.875rem; color: #6c757d;">${test.message}</div>
                            </div>
                        </div>
                        <div style="text-align: right; font-size: 0.875rem; color: #6c757d;">
                            ${test.duration}ms
                        </div>
                    </div>
                `).join('')}
            </div>

            ${this.reportData.performanceMetrics ? `
            <!-- Performance Metrics -->
            <div class="section">
                <h2>⚡ Performance Metrics</h2>
                <div class="performance-metrics">
                    ${this.reportData.performanceMetrics.coreWebVitals.lcp ? `
                        <div class="performance-metric">
                            <div class="metric-value ${this.reportData.performanceMetrics.coreWebVitals.lcp < 2500 ? 'success' : 'warning'}">${Math.round(this.reportData.performanceMetrics.coreWebVitals.lcp)}ms</div>
                            <div class="metric-label">LCP</div>
                        </div>
                    ` : ''}
                    ${this.reportData.performanceMetrics.coreWebVitals.fid ? `
                        <div class="performance-metric">
                            <div class="metric-value ${this.reportData.performanceMetrics.coreWebVitals.fid < 100 ? 'success' : 'warning'}">${Math.round(this.reportData.performanceMetrics.coreWebVitals.fid)}ms</div>
                            <div class="metric-label">FID</div>
                        </div>
                    ` : ''}
                    ${this.reportData.performanceMetrics.coreWebVitals.cls ? `
                        <div class="performance-metric">
                            <div class="metric-value ${this.reportData.performanceMetrics.coreWebVitals.cls < 0.1 ? 'success' : 'warning'}">${this.reportData.performanceMetrics.coreWebVitals.cls.toFixed(3)}</div>
                            <div class="metric-label">CLS</div>
                        </div>
                    ` : ''}
                </div>
            </div>
            ` : ''}

            ${this.reportData.issues.length > 0 ? `
            <!-- Issues Found -->
            <div class="section">
                <h2>⚠️ Issues Found</h2>
                ${this.reportData.issues.map(issue => `
                    <div class="issue ${issue.severity}">
                        <h4>${issue.test} (${issue.category})</h4>
                        <p><strong>Severity:</strong> ${issue.severity.toUpperCase()}</p>
                        <p><strong>Description:</strong> ${issue.description}</p>
                        <div class="recommendations">
                            <strong>Recommendations:</strong>
                            <ul>
                                ${issue.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `).join('')}
            </div>
            ` : ''}

            <!-- Environment Information -->
            <div class="section">
                <h2>🖥️ Test Environment</h2>
                <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
                    <p><strong>User Agent:</strong> ${this.reportData.metadata.userAgent}</p>
                    <p><strong>Screen Resolution:</strong> ${this.reportData.metadata.screenResolution}</p>
                    <p><strong>Viewport Size:</strong> ${this.reportData.metadata.viewportSize}</p>
                    <p><strong>Test Date:</strong> ${new Date(this.reportData.metadata.timestamp).toLocaleString('id-ID')}</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;

        return html;
    }

    // Generate JSON report
    generateJSONReport() {
        return JSON.stringify(this.reportData, null, 2);
    }

    // Generate CSV report
    generateCSVReport() {
        const headers = ['Test Name', 'Category', 'Status', 'Duration (ms)', 'Message'];
        const rows = this.reportData.testResults.map(test => [
            test.name,
            test.category,
            test.status,
            test.duration,
            test.message.replace(/,/g, ';') // Replace commas to avoid CSV issues
        ]);

        return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    // Download report in specified format
    downloadReport(format = 'html') {
        let content, filename, mimeType;

        switch (format.toLowerCase()) {
            case 'html':
                content = this.generateHTMLReport();
                filename = `test-report-${new Date().toISOString().split('T')[0]}.html`;
                mimeType = 'text/html';
                break;
            case 'json':
                content = this.generateJSONReport();
                filename = `test-report-${new Date().toISOString().split('T')[0]}.json`;
                mimeType = 'application/json';
                break;
            case 'csv':
                content = this.generateCSVReport();
                filename = `test-report-${new Date().toISOString().split('T')[0]}.csv`;
                mimeType = 'text/csv';
                break;
            default:
                throw new Error('Unsupported format. Use html, json, or csv.');
        }

        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
    }

    // Print report
    printReport() {
        const reportWindow = window.open('', '_blank');
        reportWindow.document.write(this.generateHTMLReport());
        reportWindow.document.close();
        reportWindow.print();
    }

    // Email report (opens email client)
    emailReport() {
        const subject = encodeURIComponent('Test Report - Agen Air Fazza');
        const body = encodeURIComponent(`
Test Report Summary:
- Total Tests: ${this.reportData.summary.totalTests}
- Passed: ${this.reportData.summary.passed}
- Failed: ${this.reportData.summary.failed}
- Success Rate: ${this.reportData.summary.successRate}%

Generated: ${new Date(this.reportData.metadata.timestamp).toLocaleString('id-ID')}
URL: ${this.reportData.metadata.url}

Please find the detailed HTML report attached.
        `);

        window.location.href = `mailto:?subject=${subject}&body=${body}`;
    }
}

// Export for use in other scripts
if (typeof window !== 'undefined') {
    window.TestReportGenerator = TestReportGenerator;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = TestReportGenerator;
}
